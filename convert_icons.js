// Simple icon generator that creates base64 PNG data
// You can run this in browser console or Node.js

function generateIconDataURL(size) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');

    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#6366f1');
    gradient.addColorStop(1, '#8b5cf6');

    // Fill background with rounded corners
    const radius = size * 0.15;
    const margin = size * 0.06;

    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.roundRect(margin, margin, size - 2*margin, size - 2*margin, radius);
    ctx.fill();

    // Add playlist lines
    ctx.fillStyle = 'white';
    const lineHeight = size * 0.03;
    const lineWidth = size * 0.4;
    const startX = size * 0.25;
    const startY = size * 0.3;
    const spacing = size * 0.1;

    for (let i = 0; i < 4; i++) {
        ctx.fillRect(startX, startY + i * spacing, lineWidth, lineHeight);
    }

    // Add play button
    const playSize = size * 0.25;
    const playX = size * 0.65;
    const playY = size * 0.65;

    // Circle background
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.beginPath();
    ctx.arc(playX + playSize/2, playY + playSize/2, playSize/2, 0, 2 * Math.PI);
    ctx.fill();

    // Triangle
    ctx.fillStyle = '#6366f1';
    ctx.beginPath();
    const triangleSize = playSize * 0.4;
    const centerX = playX + playSize/2;
    const centerY = playY + playSize/2;
    ctx.moveTo(centerX - triangleSize/3, centerY - triangleSize/2);
    ctx.lineTo(centerX - triangleSize/3, centerY + triangleSize/2);
    ctx.lineTo(centerX + triangleSize/2, centerY);
    ctx.closePath();
    ctx.fill();

    return canvas.toDataURL('image/png');
}

// Generate all sizes and create download links
function generateAllIcons() {
    const sizes = [16, 32, 48, 128];
    const container = document.createElement('div');
    container.style.cssText = 'position: fixed; top: 20px; right: 20px; background: white; padding: 20px; border: 2px solid #ccc; border-radius: 10px; z-index: 10000; max-width: 300px;';

    container.innerHTML = '<h3>Chrome Extension Icons</h3><p>Right-click each link and "Save link as..." to download:</p>';

    sizes.forEach(size => {
        const dataURL = generateIconDataURL(size);
        const link = document.createElement('a');
        link.href = dataURL;
        link.download = `icon${size}.png`;
        link.textContent = `📥 Download icon${size}.png (${size}x${size})`;
        link.style.cssText = 'display: block; margin: 10px 0; padding: 8px; background: #f0f0f0; text-decoration: none; border-radius: 4px; color: #333;';

        link.addEventListener('click', (e) => {
            e.preventDefault();
            // Create blob and download
            fetch(dataURL)
                .then(res => res.blob())
                .then(blob => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `icon${size}.png`;
                    a.click();
                    URL.revokeObjectURL(url);
                });
        });

        container.appendChild(link);
    });

    const closeBtn = document.createElement('button');
    closeBtn.textContent = '❌ Close';
    closeBtn.style.cssText = 'margin-top: 10px; padding: 8px 16px; background: #ff4444; color: white; border: none; border-radius: 4px; cursor: pointer;';
    closeBtn.onclick = () => container.remove();
    container.appendChild(closeBtn);

    document.body.appendChild(container);
}

// Polyfill for roundRect
if (typeof CanvasRenderingContext2D !== 'undefined' && !CanvasRenderingContext2D.prototype.roundRect) {
    CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
        this.beginPath();
        this.moveTo(x + radius, y);
        this.lineTo(x + width - radius, y);
        this.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.lineTo(x + width, y + height - radius);
        this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.lineTo(x + radius, y + height);
        this.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.lineTo(x, y + radius);
        this.quadraticCurveTo(x, y, x + radius, y);
        this.closePath();
    };
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
    console.log('🎨 Icon generator loaded! Run generateAllIcons() to create download links.');
}