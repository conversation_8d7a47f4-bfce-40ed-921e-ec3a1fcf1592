// Content script for Content Playlist Generator
class ContentDetector {
    constructor() {
        this.init();
    }

    init() {
        this.setupMessageListener();
        this.injectStyles();
        this.setupPageObserver();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'detectContent') {
                const content = this.detectAllContent();
                sendResponse({ content: content });
            } else if (request.action === 'highlightContent') {
                this.highlightContent(request.selector);
                sendResponse({ success: true });
            } else if (request.action === 'getPageMetadata') {
                const metadata = this.getPageMetadata();
                sendResponse({ metadata: metadata });
            }
            return true; // Keep message channel open for async response
        });
    }

    injectStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .playlist-highlight {
                outline: 2px solid #1a73e8 !important;
                outline-offset: 2px !important;
                background-color: rgba(26, 115, 232, 0.1) !important;
            }
            
            .playlist-content-marker {
                position: absolute;
                top: -8px;
                right: -8px;
                width: 20px;
                height: 20px;
                background: #1a73e8;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                z-index: 10000;
                cursor: pointer;
            }
        `;
        document.head.appendChild(style);
    }

    setupPageObserver() {
        // Observe page changes for dynamic content
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Notify background script of page changes
                    chrome.runtime.sendMessage({
                        action: 'pageContentChanged',
                        url: window.location.href
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    detectAllContent() {
        const content = [];

        // Detect videos
        content.push(...this.detectVideos());
        
        // Detect audio
        content.push(...this.detectAudio());
        
        // Detect images
        content.push(...this.detectImages());
        
        // Detect articles/text content
        content.push(...this.detectArticles());
        
        // Detect links
        content.push(...this.detectLinks());
        
        // Detect embedded content
        content.push(...this.detectEmbeddedContent());

        return content;
    }

    detectVideos() {
        const videos = [];
        
        // HTML5 video elements
        document.querySelectorAll('video').forEach((video, index) => {
            const title = this.getElementTitle(video) || `Video ${index + 1}`;
            const src = video.src || video.currentSrc || this.getVideoSource(video);
            
            if (src) {
                videos.push({
                    type: 'video',
                    title: title,
                    url: src,
                    duration: video.duration || null,
                    thumbnail: video.poster || null,
                    selector: this.getElementSelector(video),
                    metadata: {
                        width: video.videoWidth,
                        height: video.videoHeight,
                        controls: video.controls
                    }
                });
            }
        });

        // YouTube videos
        document.querySelectorAll('iframe[src*="youtube.com"], iframe[src*="youtu.be"]').forEach((iframe, index) => {
            const title = this.getElementTitle(iframe) || `YouTube Video ${index + 1}`;
            videos.push({
                type: 'video',
                title: title,
                url: iframe.src,
                platform: 'youtube',
                selector: this.getElementSelector(iframe),
                metadata: {
                    embedded: true
                }
            });
        });

        // Vimeo videos
        document.querySelectorAll('iframe[src*="vimeo.com"]').forEach((iframe, index) => {
            const title = this.getElementTitle(iframe) || `Vimeo Video ${index + 1}`;
            videos.push({
                type: 'video',
                title: title,
                url: iframe.src,
                platform: 'vimeo',
                selector: this.getElementSelector(iframe),
                metadata: {
                    embedded: true
                }
            });
        });

        return videos;
    }

    detectAudio() {
        const audio = [];
        
        document.querySelectorAll('audio').forEach((audioEl, index) => {
            const title = this.getElementTitle(audioEl) || `Audio ${index + 1}`;
            const src = audioEl.src || audioEl.currentSrc || this.getAudioSource(audioEl);
            
            if (src) {
                audio.push({
                    type: 'audio',
                    title: title,
                    url: src,
                    duration: audioEl.duration || null,
                    selector: this.getElementSelector(audioEl),
                    metadata: {
                        controls: audioEl.controls,
                        autoplay: audioEl.autoplay
                    }
                });
            }
        });

        // Spotify embeds
        document.querySelectorAll('iframe[src*="spotify.com"]').forEach((iframe, index) => {
            const title = this.getElementTitle(iframe) || `Spotify Track ${index + 1}`;
            audio.push({
                type: 'audio',
                title: title,
                url: iframe.src,
                platform: 'spotify',
                selector: this.getElementSelector(iframe),
                metadata: {
                    embedded: true
                }
            });
        });

        return audio;
    }

    detectImages() {
        const images = [];
        
        document.querySelectorAll('img').forEach((img, index) => {
            // Skip small images (likely icons or decorative)
            if (img.width < 100 || img.height < 100) return;
            
            const title = img.alt || img.title || `Image ${index + 1}`;
            const src = img.src || img.dataset.src;
            
            if (src && !src.startsWith('data:')) {
                images.push({
                    type: 'image',
                    title: title,
                    url: src,
                    selector: this.getElementSelector(img),
                    metadata: {
                        width: img.naturalWidth || img.width,
                        height: img.naturalHeight || img.height,
                        alt: img.alt
                    }
                });
            }
        });

        return images.slice(0, 10); // Limit to first 10 images
    }

    detectArticles() {
        const articles = [];
        
        // Look for article elements
        document.querySelectorAll('article').forEach((article, index) => {
            const title = this.getArticleTitle(article) || `Article ${index + 1}`;
            const content = this.getArticleContent(article);
            
            if (content && content.length > 100) {
                articles.push({
                    type: 'article',
                    title: title,
                    url: window.location.href,
                    selector: this.getElementSelector(article),
                    metadata: {
                        wordCount: content.split(' ').length,
                        excerpt: content.substring(0, 200) + '...'
                    }
                });
            }
        });

        // Look for main content areas
        const mainContent = document.querySelector('main, [role="main"], .main-content, #main-content');
        if (mainContent && articles.length === 0) {
            const title = document.title || 'Main Content';
            const content = this.getArticleContent(mainContent);
            
            if (content && content.length > 200) {
                articles.push({
                    type: 'article',
                    title: title,
                    url: window.location.href,
                    selector: this.getElementSelector(mainContent),
                    metadata: {
                        wordCount: content.split(' ').length,
                        excerpt: content.substring(0, 200) + '...'
                    }
                });
            }
        }

        return articles;
    }

    detectLinks() {
        const links = [];
        
        // Look for significant links (not navigation)
        document.querySelectorAll('a[href]').forEach((link, index) => {
            const href = link.href;
            const text = link.textContent.trim();
            
            // Skip empty links, anchors, and navigation links
            if (!text || href.startsWith('#') || href === window.location.href) return;
            if (link.closest('nav, .nav, .navigation, .menu')) return;
            if (text.length < 5 || text.length > 100) return;
            
            // Prioritize links that look like articles or content
            const isContentLink = /\b(article|post|story|blog|news|read|more|continue)\b/i.test(text) ||
                                 /\b(article|post|story|blog|news)\b/i.test(href);
            
            if (isContentLink || links.length < 5) {
                links.push({
                    type: 'link',
                    title: text,
                    url: href,
                    selector: this.getElementSelector(link),
                    metadata: {
                        external: !href.startsWith(window.location.origin),
                        isContentLink: isContentLink
                    }
                });
            }
        });

        return links.slice(0, 10); // Limit to 10 links
    }

    detectEmbeddedContent() {
        const embedded = [];
        
        // Twitter embeds
        document.querySelectorAll('iframe[src*="twitter.com"], blockquote.twitter-tweet').forEach((el, index) => {
            embedded.push({
                type: 'social',
                title: `Twitter Post ${index + 1}`,
                url: el.src || el.querySelector('a')?.href || window.location.href,
                platform: 'twitter',
                selector: this.getElementSelector(el)
            });
        });

        // Instagram embeds
        document.querySelectorAll('iframe[src*="instagram.com"], blockquote[data-instgrm-permalink]').forEach((el, index) => {
            embedded.push({
                type: 'social',
                title: `Instagram Post ${index + 1}`,
                url: el.src || el.dataset.instgrmPermalink || window.location.href,
                platform: 'instagram',
                selector: this.getElementSelector(el)
            });
        });

        // Generic iframes
        document.querySelectorAll('iframe').forEach((iframe, index) => {
            const src = iframe.src;
            if (!src || embedded.some(e => e.url === src)) return;
            
            // Skip already detected platforms
            if (src.includes('youtube.com') || src.includes('vimeo.com') || 
                src.includes('spotify.com') || src.includes('twitter.com') || 
                src.includes('instagram.com')) return;
            
            embedded.push({
                type: 'embedded',
                title: iframe.title || `Embedded Content ${index + 1}`,
                url: src,
                selector: this.getElementSelector(iframe),
                metadata: {
                    width: iframe.width,
                    height: iframe.height
                }
            });
        });

        return embedded;
    }

    getPageMetadata() {
        return {
            title: document.title,
            url: window.location.href,
            description: document.querySelector('meta[name="description"]')?.content || '',
            keywords: document.querySelector('meta[name="keywords"]')?.content || '',
            author: document.querySelector('meta[name="author"]')?.content || '',
            publishedTime: document.querySelector('meta[property="article:published_time"]')?.content || '',
            modifiedTime: document.querySelector('meta[property="article:modified_time"]')?.content || '',
            siteName: document.querySelector('meta[property="og:site_name"]')?.content || '',
            type: document.querySelector('meta[property="og:type"]')?.content || 'website',
            image: document.querySelector('meta[property="og:image"]')?.content || '',
            favicon: document.querySelector('link[rel="icon"]')?.href || 
                    document.querySelector('link[rel="shortcut icon"]')?.href || ''
        };
    }

    // Helper methods
    getElementTitle(element) {
        return element.title || 
               element.alt || 
               element.getAttribute('aria-label') ||
               element.closest('[title]')?.title ||
               element.textContent?.trim().substring(0, 50);
    }

    getVideoSource(video) {
        const source = video.querySelector('source');
        return source ? source.src : null;
    }

    getAudioSource(audio) {
        const source = audio.querySelector('source');
        return source ? source.src : null;
    }

    getArticleTitle(article) {
        const titleEl = article.querySelector('h1, h2, h3, .title, .headline');
        return titleEl ? titleEl.textContent.trim() : null;
    }

    getArticleContent(element) {
        // Clone element to avoid modifying original
        const clone = element.cloneNode(true);
        
        // Remove script, style, and navigation elements
        clone.querySelectorAll('script, style, nav, .nav, .navigation, .menu, .sidebar, .ads, .advertisement').forEach(el => el.remove());
        
        return clone.textContent.trim();
    }

    getElementSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }
        
        if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
                return `.${classes[0]}`;
            }
        }
        
        const tagName = element.tagName.toLowerCase();
        const parent = element.parentElement;
        if (parent) {
            const siblings = Array.from(parent.children).filter(child => child.tagName === element.tagName);
            if (siblings.length > 1) {
                const index = siblings.indexOf(element) + 1;
                return `${tagName}:nth-of-type(${index})`;
            }
        }
        
        return tagName;
    }

    highlightContent(selector) {
        // Remove existing highlights
        document.querySelectorAll('.playlist-highlight').forEach(el => {
            el.classList.remove('playlist-highlight');
        });

        // Add highlight to selected element
        const element = document.querySelector(selector);
        if (element) {
            element.classList.add('playlist-highlight');
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
}

// Initialize content detector
new ContentDetector();
