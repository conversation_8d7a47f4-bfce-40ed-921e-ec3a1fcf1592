<!DOCTYPE html>
<html>
<head>
    <title>URGENT: Fix Chrome Extension Icons</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f44336; 
            color: white; 
            text-align: center;
        }
        .container { 
            max-width: 500px; 
            margin: 0 auto; 
            background: white; 
            color: black; 
            padding: 30px; 
            border-radius: 15px; 
        }
        .urgent { 
            background: #ff5722; 
            color: white; 
            padding: 15px; 
            border-radius: 10px; 
            margin-bottom: 20px; 
            font-size: 18px; 
            font-weight: bold;
        }
        .fix-button { 
            background: #4CAF50; 
            color: white; 
            border: none; 
            padding: 20px 40px; 
            font-size: 20px; 
            border-radius: 10px; 
            cursor: pointer; 
            width: 100%; 
            margin: 20px 0;
        }
        .fix-button:hover { 
            background: #45a049; 
        }
        .step { 
            background: #e3f2fd; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="urgent">
            🚨 CHROME EXTENSION ERROR 🚨<br>
            Missing Icon Files
        </div>
        
        <h2>Quick Fix Solution</h2>
        <p>Your extension can't load because it's missing icon files.</p>
        
        <button class="fix-button" onclick="fixNow()">
            🔧 FIX NOW - Download All Icons
        </button>
        
        <div id="status"></div>
        
        <div class="step">
            <strong>After clicking the button:</strong><br>
            1. 4 icon files will download automatically<br>
            2. Move them to your extension's <code>assets/</code> folder<br>
            3. Refresh your extension in Chrome<br>
            4. Error will be fixed! ✅
        </div>
    </div>

    <script>
        function createSimpleIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Purple background
            ctx.fillStyle = '#6366f1';
            ctx.fillRect(0, 0, size, size);
            
            // White letter P
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.7}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('P', size / 2, size / 2);
            
            return canvas;
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function fixNow() {
            const status = document.getElementById('status');
            status.innerHTML = '<div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">🔄 Creating and downloading icons...</div>';
            
            const sizes = [16, 32, 48, 128];
            let completed = 0;
            
            sizes.forEach((size, index) => {
                setTimeout(() => {
                    const canvas = createSimpleIcon(size);
                    downloadCanvas(canvas, `icon${size}.png`);
                    completed++;
                    
                    if (completed === sizes.length) {
                        status.innerHTML = `
                            <div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; color: #155724;">
                                <strong>✅ SUCCESS!</strong><br>
                                All 4 icon files downloaded!<br><br>
                                <strong>Next steps:</strong><br>
                                1. Check your Downloads folder<br>
                                2. Move all 4 icon files to your extension's assets/ folder<br>
                                3. Go to chrome://extensions/<br>
                                4. Click refresh on your extension<br>
                                5. Done! 🎉
                            </div>
                        `;
                    }
                }, index * 500);
            });
        }
        
        // Auto-run on page load for emergency fix
        window.onload = function() {
            setTimeout(() => {
                if (confirm('🚨 EMERGENCY FIX: Auto-download icons now?\n\nThis will immediately download the 4 required icon files to fix your Chrome extension error.')) {
                    fixNow();
                }
            }, 1000);
        };
    </script>
</body>
</html>
