/* Content script styles for Content Playlist Generator */

/* Highlight styles for detected content */
.playlist-highlight {
    outline: 2px solid #1a73e8 !important;
    outline-offset: 2px !important;
    background-color: rgba(26, 115, 232, 0.1) !important;
    transition: all 0.3s ease !important;
}

/* Content marker for interactive elements */
.playlist-content-marker {
    position: absolute !important;
    top: -8px !important;
    right: -8px !important;
    width: 20px !important;
    height: 20px !important;
    background: #1a73e8 !important;
    color: white !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 12px !important;
    font-weight: bold !important;
    z-index: 10000 !important;
    cursor: pointer !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.2s ease !important;
}

.playlist-content-marker:hover {
    background: #1557b0 !important;
    transform: scale(1.1) !important;
}

/* Floating action button for quick add */
.playlist-fab {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    width: 56px !important;
    height: 56px !important;
    background: #1a73e8 !important;
    color: white !important;
    border: none !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 24px !important;
    cursor: pointer !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    z-index: 9999 !important;
    transition: all 0.3s ease !important;
    opacity: 0 !important;
    transform: scale(0) !important;
}

.playlist-fab.visible {
    opacity: 1 !important;
    transform: scale(1) !important;
}

.playlist-fab:hover {
    background: #1557b0 !important;
    transform: scale(1.1) !important;
}

/* Selection overlay */
.playlist-selection-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 10001 !important;
    display: none !important;
}

.playlist-selection-overlay.active {
    display: block !important;
}

/* Selection tooltip */
.playlist-tooltip {
    position: absolute !important;
    background: #333 !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    z-index: 10002 !important;
    pointer-events: none !important;
    opacity: 0 !important;
    transition: opacity 0.2s ease !important;
}

.playlist-tooltip.visible {
    opacity: 1 !important;
}

.playlist-tooltip::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    margin-left: -5px !important;
    border-width: 5px !important;
    border-style: solid !important;
    border-color: #333 transparent transparent transparent !important;
}

/* Content detection indicators */
.playlist-video-indicator::before {
    content: '🎥' !important;
}

.playlist-audio-indicator::before {
    content: '🎵' !important;
}

.playlist-image-indicator::before {
    content: '🖼️' !important;
}

.playlist-article-indicator::before {
    content: '📄' !important;
}

.playlist-link-indicator::before {
    content: '🔗' !important;
}

.playlist-social-indicator::before {
    content: '📱' !important;
}

/* Hover effects for detectable content */
video:hover,
audio:hover,
img:hover,
article:hover,
iframe:hover {
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.3) !important;
    transition: box-shadow 0.2s ease !important;
}

/* Mini popup for quick actions */
.playlist-mini-popup {
    position: absolute !important;
    background: white !important;
    border: 1px solid #dadce0 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    padding: 12px !important;
    z-index: 10003 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 14px !important;
    min-width: 200px !important;
    opacity: 0 !important;
    transform: translateY(-10px) !important;
    transition: all 0.2s ease !important;
    pointer-events: none !important;
}

.playlist-mini-popup.visible {
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
}

.playlist-mini-popup-title {
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    color: #202124 !important;
}

.playlist-mini-popup-actions {
    display: flex !important;
    gap: 8px !important;
}

.playlist-mini-popup-btn {
    padding: 6px 12px !important;
    border: none !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.playlist-mini-popup-btn.primary {
    background: #1a73e8 !important;
    color: white !important;
}

.playlist-mini-popup-btn.primary:hover {
    background: #1557b0 !important;
}

.playlist-mini-popup-btn.secondary {
    background: #f8f9fa !important;
    color: #5f6368 !important;
    border: 1px solid #dadce0 !important;
}

.playlist-mini-popup-btn.secondary:hover {
    background: #f1f3f4 !important;
}

/* Progress indicator */
.playlist-progress {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 3px !important;
    background: rgba(26, 115, 232, 0.2) !important;
    z-index: 10004 !important;
    opacity: 0 !important;
    transition: opacity 0.2s ease !important;
}

.playlist-progress.visible {
    opacity: 1 !important;
}

.playlist-progress-bar {
    height: 100% !important;
    background: #1a73e8 !important;
    width: 0% !important;
    transition: width 0.3s ease !important;
}

/* Notification styles */
.playlist-notification {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: #4caf50 !important;
    color: white !important;
    padding: 12px 16px !important;
    border-radius: 6px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 14px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    z-index: 10005 !important;
    opacity: 0 !important;
    transform: translateX(100%) !important;
    transition: all 0.3s ease !important;
}

.playlist-notification.visible {
    opacity: 1 !important;
    transform: translateX(0) !important;
}

.playlist-notification.error {
    background: #f44336 !important;
}

.playlist-notification.warning {
    background: #ff9800 !important;
}

.playlist-notification.info {
    background: #2196f3 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .playlist-fab {
        bottom: 15px !important;
        right: 15px !important;
        width: 48px !important;
        height: 48px !important;
        font-size: 20px !important;
    }
    
    .playlist-mini-popup {
        min-width: 180px !important;
        font-size: 13px !important;
    }
    
    .playlist-notification {
        top: 15px !important;
        right: 15px !important;
        left: 15px !important;
        font-size: 13px !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .playlist-mini-popup {
        background: #2d2d2d !important;
        border-color: #5f6368 !important;
        color: #e8eaed !important;
    }
    
    .playlist-mini-popup-title {
        color: #e8eaed !important;
    }
    
    .playlist-tooltip {
        background: #5f6368 !important;
    }
    
    .playlist-tooltip::after {
        border-color: #5f6368 transparent transparent transparent !important;
    }
}
