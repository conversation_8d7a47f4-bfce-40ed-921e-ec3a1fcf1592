# Assets Folder

This folder contains the icons and other assets for the Content Playlist Generator Chrome extension.

## Required Icons

You need to create the following icon files:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (Windows)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Icon Guidelines

- Use a consistent design across all sizes
- Ensure good visibility on both light and dark backgrounds
- Consider using a playlist or content-related symbol (e.g., list with play button, stacked documents, etc.)
- Follow Chrome extension icon best practices
- Use PNG format with transparency where appropriate

## Suggested Design Elements

- Playlist/list icon (📋 or similar)
- Play button overlay (▶️)
- Content symbols (video, audio, text)
- Modern, clean design
- Brand colors: Primary #1a73e8 (Google Blue)

## Creating Icons

You can create these icons using:
- Design tools like Figma, Sketch, or Adobe Illustrator
- Online icon generators
- Icon libraries like Material Icons or Feather Icons
- AI image generators with specific prompts

## Placeholder Icons

For development purposes, you can use simple colored squares or download free icons from:
- Material Design Icons
- Feather Icons
- Heroicons
- Font Awesome (free icons)

Make sure to replace these with proper branded icons before publishing the extension.
