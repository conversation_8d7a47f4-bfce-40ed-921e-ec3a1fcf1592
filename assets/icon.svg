<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background with rounded corners -->
  <rect x="8" y="8" width="112" height="112" rx="20" ry="20" fill="url(#bg-gradient)"/>
  
  <!-- Playlist icon -->
  <g fill="white">
    <!-- List lines -->
    <rect x="32" y="40" width="48" height="4" rx="2"/>
    <rect x="32" y="52" width="48" height="4" rx="2"/>
    <rect x="32" y="64" width="48" height="4" rx="2"/>
    <rect x="32" y="76" width="48" height="4" rx="2"/>
    
    <!-- Play button overlay -->
    <circle cx="88" cy="88" r="16" fill="rgba(255,255,255,0.9)"/>
    <polygon points="84,80 84,96 96,88" fill="#6366f1"/>
  </g>
</svg>
