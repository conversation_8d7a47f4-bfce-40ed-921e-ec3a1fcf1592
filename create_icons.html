<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator for Chrome Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .icon {
            margin-bottom: 10px;
        }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #5b5bd6;
        }
        .download-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Chrome Extension Icon Generator</h1>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>Click "Generate Icons" to create placeholder icons</li>
                <li>Download each icon by right-clicking and "Save image as..."</li>
                <li>Save them in your extension's <code>assets/</code> folder with exact names</li>
                <li>Reload your extension in Chrome</li>
            </ol>
        </div>

        <div class="icon-preview" id="iconPreview">
            <p>Click "Generate Icons" to create the required icon files</p>
        </div>

        <button onclick="generateIcons()">🎯 Generate Icons</button>
        <button onclick="downloadAll()">📥 Download All Icons</button>
        <button onclick="createQuickFix()">⚡ Quick Fix - Create Basic Icons</button>

        <div class="download-section">
            <h3>Required Icon Files:</h3>
            <ul>
                <li><strong>icon16.png</strong> - 16x16 pixels (toolbar)</li>
                <li><strong>icon32.png</strong> - 32x32 pixels (Windows)</li>
                <li><strong>icon48.png</strong> - 48x48 pixels (extension management)</li>
                <li><strong>icon128.png</strong> - 128x128 pixels (Chrome Web Store)</li>
            </ul>
        </div>
    </div>

    <script>
        const sizes = [16, 32, 48, 128];
        const canvases = {};

        function generateIcons() {
            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '';

            sizes.forEach(size => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');

                // Create gradient background
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#6366f1');
                gradient.addColorStop(1, '#8b5cf6');
                
                // Fill background
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, size, size);

                // Add rounded corners
                ctx.globalCompositeOperation = 'destination-in';
                ctx.beginPath();
                const radius = size * 0.2;
                ctx.roundRect(0, 0, size, size, radius);
                ctx.fill();
                ctx.globalCompositeOperation = 'source-over';

                // Add playlist icon
                ctx.fillStyle = 'white';
                ctx.font = `bold ${size * 0.5}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('📋', size / 2, size / 2);

                // Store canvas
                canvases[size] = canvas;

                // Create preview
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                iconItem.innerHTML = `
                    <div class="icon">${canvas.outerHTML}</div>
                    <div><strong>icon${size}.png</strong></div>
                    <div>${size}x${size} pixels</div>
                    <button onclick="downloadIcon(${size})">Download</button>
                `;
                preview.appendChild(iconItem);
            });
        }

        function downloadIcon(size) {
            const canvas = canvases[size];
            if (canvas) {
                const link = document.createElement('a');
                link.download = `icon${size}.png`;
                link.href = canvas.toDataURL();
                link.click();
            }
        }

        function downloadAll() {
            sizes.forEach(size => {
                setTimeout(() => downloadIcon(size), size * 10);
            });
        }

        function createQuickFix() {
            // Create simple colored squares as emergency icons
            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '<h3>🚀 Quick Fix Icons (Simple colored squares)</h3>';

            sizes.forEach(size => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');

                // Simple gradient background
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#6366f1');
                gradient.addColorStop(1, '#8b5cf6');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, size, size);

                // Add simple white text
                ctx.fillStyle = 'white';
                ctx.font = `bold ${Math.max(8, size * 0.3)}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('P', size / 2, size / 2);

                // Store canvas
                canvases[size] = canvas;

                // Auto-download
                setTimeout(() => {
                    const link = document.createElement('a');
                    link.download = `icon${size}.png`;
                    link.href = canvas.toDataURL();
                    link.click();
                }, size * 10);

                // Create preview
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                iconItem.innerHTML = `
                    <div class="icon">${canvas.outerHTML}</div>
                    <div><strong>icon${size}.png</strong></div>
                    <div>✅ Downloaded</div>
                `;
                preview.appendChild(iconItem);
            });

            // Show success message
            setTimeout(() => {
                alert('✅ All icons downloaded! Check your Downloads folder and move them to the assets/ folder in your extension directory.');
            }, 1000);
        }

        // Polyfill for roundRect if not available
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
