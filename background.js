// Background script for Content Playlist Generator
class PlaylistBackground {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupContextMenus();
        this.initializeStorage();
    }

    setupEventListeners() {
        // Extension installation/startup
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });

        chrome.runtime.onStartup.addListener(() => {
            this.handleStartup();
        });

        // Message handling
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Tab events
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        chrome.tabs.onActivated.addListener((activeInfo) => {
            this.handleTabActivated(activeInfo);
        });

        // Context menu clicks
        chrome.contextMenus.onClicked.addListener((info, tab) => {
            this.handleContextMenuClick(info, tab);
        });

        // Storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });
    }

    setupContextMenus() {
        chrome.contextMenus.removeAll(() => {
            // Main context menu
            chrome.contextMenus.create({
                id: 'addToPlaylist',
                title: 'Add to Playlist',
                contexts: ['page', 'selection', 'link', 'image', 'video', 'audio']
            });

            // Submenu for specific content types
            chrome.contextMenus.create({
                id: 'addPageToPlaylist',
                parentId: 'addToPlaylist',
                title: 'Add Current Page',
                contexts: ['page']
            });

            chrome.contextMenus.create({
                id: 'addSelectionToPlaylist',
                parentId: 'addToPlaylist',
                title: 'Add Selected Text',
                contexts: ['selection']
            });

            chrome.contextMenus.create({
                id: 'addLinkToPlaylist',
                parentId: 'addToPlaylist',
                title: 'Add Link',
                contexts: ['link']
            });

            chrome.contextMenus.create({
                id: 'addImageToPlaylist',
                parentId: 'addToPlaylist',
                title: 'Add Image',
                contexts: ['image']
            });

            chrome.contextMenus.create({
                id: 'addVideoToPlaylist',
                parentId: 'addToPlaylist',
                title: 'Add Video',
                contexts: ['video']
            });

            chrome.contextMenus.create({
                id: 'addAudioToPlaylist',
                parentId: 'addToPlaylist',
                title: 'Add Audio',
                contexts: ['audio']
            });

            // Separator
            chrome.contextMenus.create({
                id: 'separator1',
                parentId: 'addToPlaylist',
                type: 'separator'
            });

            chrome.contextMenus.create({
                id: 'detectContent',
                parentId: 'addToPlaylist',
                title: 'Detect All Content',
                contexts: ['page']
            });

            chrome.contextMenus.create({
                id: 'openPlaylistManager',
                parentId: 'addToPlaylist',
                title: 'Open Playlist Manager',
                contexts: ['page']
            });
        });
    }

    async initializeStorage() {
        try {
            const result = await chrome.storage.local.get(['playlists', 'settings']);
            
            if (!result.playlists) {
                await chrome.storage.local.set({ playlists: [] });
            }

            if (!result.settings) {
                const defaultSettings = {
                    autoDetect: true,
                    showNotifications: true,
                    defaultPlaylist: null,
                    syncEnabled: false,
                    theme: 'auto'
                };
                await chrome.storage.local.set({ settings: defaultSettings });
            }
        } catch (error) {
            console.error('Error initializing storage:', error);
        }
    }

    handleInstallation(details) {
        if (details.reason === 'install') {
            // First time installation
            this.showWelcomeNotification();
            this.openWelcomePage();
        } else if (details.reason === 'update') {
            // Extension updated
            this.handleUpdate(details.previousVersion);
        }
    }

    handleStartup() {
        // Extension started
        this.syncPlaylists();
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'addToPlaylist':
                    const result = await this.addToPlaylist(request.item, request.playlistId);
                    sendResponse({ success: true, result });
                    break;

                case 'createPlaylist':
                    const playlist = await this.createPlaylist(request.playlist);
                    sendResponse({ success: true, playlist });
                    break;

                case 'getPlaylists':
                    const playlists = await this.getPlaylists();
                    sendResponse({ success: true, playlists });
                    break;

                case 'deletePlaylist':
                    await this.deletePlaylist(request.playlistId);
                    sendResponse({ success: true });
                    break;

                case 'openPlaylist':
                    await this.openPlaylist(request.playlistId);
                    sendResponse({ success: true });
                    break;

                case 'openSettings':
                    await this.openSettings();
                    sendResponse({ success: true });
                    break;

                case 'exportPlaylists':
                    const exportData = await this.exportPlaylists();
                    sendResponse({ success: true, data: exportData });
                    break;

                case 'importPlaylists':
                    await this.importPlaylists(request.data);
                    sendResponse({ success: true });
                    break;

                case 'pageContentChanged':
                    await this.handlePageContentChanged(sender.tab, request.url);
                    sendResponse({ success: true });
                    break;

                case 'getPageMetadata':
                    const metadata = await this.getPageMetadata(sender.tab.id);
                    sendResponse({ success: true, metadata });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        if (changeInfo.status === 'complete' && tab.url) {
            // Page loaded, check for auto-detection
            this.checkAutoDetection(tab);
        }
    }

    handleTabActivated(activeInfo) {
        // Tab switched, update badge if needed
        this.updateBadge(activeInfo.tabId);
    }

    async handleContextMenuClick(info, tab) {
        try {
            switch (info.menuItemId) {
                case 'addPageToPlaylist':
                    await this.addPageToPlaylist(tab);
                    break;

                case 'addSelectionToPlaylist':
                    await this.addSelectionToPlaylist(info, tab);
                    break;

                case 'addLinkToPlaylist':
                    await this.addLinkToPlaylist(info, tab);
                    break;

                case 'addImageToPlaylist':
                    await this.addImageToPlaylist(info, tab);
                    break;

                case 'addVideoToPlaylist':
                    await this.addVideoToPlaylist(info, tab);
                    break;

                case 'addAudioToPlaylist':
                    await this.addAudioToPlaylist(info, tab);
                    break;

                case 'detectContent':
                    await this.detectContentInTab(tab.id);
                    break;

                case 'openPlaylistManager':
                    await this.openPlaylistManager();
                    break;
            }
        } catch (error) {
            console.error('Error handling context menu click:', error);
            this.showNotification('Error: ' + error.message, 'error');
        }
    }

    handleStorageChange(changes, namespace) {
        if (namespace === 'local' && changes.playlists) {
            // Playlists changed, notify all tabs
            this.notifyPlaylistsChanged();
        }
    }

    async addToPlaylist(item, playlistId = null) {
        const playlists = await this.getPlaylists();
        
        // If no playlist specified, use default or first playlist
        if (!playlistId) {
            const settings = await this.getSettings();
            playlistId = settings.defaultPlaylist || (playlists.length > 0 ? playlists[0].id : null);
        }

        if (!playlistId) {
            throw new Error('No playlist available. Create a playlist first.');
        }

        const playlist = playlists.find(p => p.id === playlistId);
        if (!playlist) {
            throw new Error('Playlist not found');
        }

        // Add item to playlist
        const playlistItem = {
            id: Date.now().toString(),
            ...item,
            addedAt: new Date().toISOString()
        };

        playlist.items.push(playlistItem);
        playlist.updated = new Date().toISOString();

        await chrome.storage.local.set({ playlists });
        
        this.showNotification(`Added to "${playlist.name}"`);
        return playlistItem;
    }

    async createPlaylist(playlistData) {
        const playlists = await this.getPlaylists();
        
        const newPlaylist = {
            id: Date.now().toString(),
            name: playlistData.name,
            description: playlistData.description || '',
            items: [],
            created: new Date().toISOString(),
            updated: new Date().toISOString(),
            ...playlistData
        };

        playlists.push(newPlaylist);
        await chrome.storage.local.set({ playlists });
        
        return newPlaylist;
    }

    async getPlaylists() {
        const result = await chrome.storage.local.get(['playlists']);
        return result.playlists || [];
    }

    async getSettings() {
        const result = await chrome.storage.local.get(['settings']);
        return result.settings || {};
    }

    async deletePlaylist(playlistId) {
        const playlists = await this.getPlaylists();
        const filteredPlaylists = playlists.filter(p => p.id !== playlistId);
        await chrome.storage.local.set({ playlists: filteredPlaylists });
    }

    async openPlaylist(playlistId) {
        // Open playlist in new tab (could be a web app or extension page)
        const url = chrome.runtime.getURL(`playlist.html?id=${playlistId}`);
        await chrome.tabs.create({ url });
    }

    async openSettings() {
        const url = chrome.runtime.getURL('settings.html');
        await chrome.tabs.create({ url });
    }

    async openPlaylistManager() {
        const url = chrome.runtime.getURL('manager.html');
        await chrome.tabs.create({ url });
    }

    async exportPlaylists() {
        const playlists = await this.getPlaylists();
        return {
            version: '1.0',
            exportDate: new Date().toISOString(),
            playlists: playlists
        };
    }

    async importPlaylists(data) {
        if (!data.playlists || !Array.isArray(data.playlists)) {
            throw new Error('Invalid import data');
        }

        const existingPlaylists = await this.getPlaylists();
        const mergedPlaylists = [...existingPlaylists, ...data.playlists];
        
        await chrome.storage.local.set({ playlists: mergedPlaylists });
    }

    async addPageToPlaylist(tab) {
        const item = {
            type: 'webpage',
            title: tab.title,
            url: tab.url,
            favicon: tab.favIconUrl
        };
        
        await this.addToPlaylist(item);
    }

    async addSelectionToPlaylist(info, tab) {
        const item = {
            type: 'text',
            title: info.selectionText.substring(0, 50) + '...',
            content: info.selectionText,
            url: tab.url,
            pageTitle: tab.title
        };
        
        await this.addToPlaylist(item);
    }

    async addLinkToPlaylist(info, tab) {
        const item = {
            type: 'link',
            title: info.linkText || 'Link',
            url: info.linkUrl,
            sourceUrl: tab.url,
            sourceTitle: tab.title
        };
        
        await this.addToPlaylist(item);
    }

    async addImageToPlaylist(info, tab) {
        const item = {
            type: 'image',
            title: 'Image from ' + tab.title,
            url: info.srcUrl,
            sourceUrl: tab.url,
            sourceTitle: tab.title
        };
        
        await this.addToPlaylist(item);
    }

    async addVideoToPlaylist(info, tab) {
        const item = {
            type: 'video',
            title: 'Video from ' + tab.title,
            url: info.srcUrl,
            sourceUrl: tab.url,
            sourceTitle: tab.title
        };
        
        await this.addToPlaylist(item);
    }

    async addAudioToPlaylist(info, tab) {
        const item = {
            type: 'audio',
            title: 'Audio from ' + tab.title,
            url: info.srcUrl,
            sourceUrl: tab.url,
            sourceTitle: tab.title
        };
        
        await this.addToPlaylist(item);
    }

    async detectContentInTab(tabId) {
        try {
            const response = await chrome.tabs.sendMessage(tabId, {
                action: 'detectContent'
            });
            
            if (response && response.content && response.content.length > 0) {
                this.showNotification(`Found ${response.content.length} items`);
            } else {
                this.showNotification('No content detected');
            }
        } catch (error) {
            console.error('Error detecting content:', error);
        }
    }

    async checkAutoDetection(tab) {
        const settings = await this.getSettings();
        if (settings.autoDetect) {
            // Auto-detect content on page load
            setTimeout(() => {
                this.detectContentInTab(tab.id);
            }, 2000); // Wait 2 seconds for page to fully load
        }
    }

    async updateBadge(tabId) {
        // Update extension badge with playlist count or other info
        const playlists = await this.getPlaylists();
        const totalItems = playlists.reduce((sum, playlist) => sum + playlist.items.length, 0);
        
        if (totalItems > 0) {
            chrome.action.setBadgeText({
                text: totalItems.toString(),
                tabId: tabId
            });
            chrome.action.setBadgeBackgroundColor({ color: '#1a73e8' });
        } else {
            chrome.action.setBadgeText({ text: '', tabId: tabId });
        }
    }

    async getPageMetadata(tabId) {
        try {
            const response = await chrome.tabs.sendMessage(tabId, {
                action: 'getPageMetadata'
            });
            return response.metadata;
        } catch (error) {
            console.error('Error getting page metadata:', error);
            return null;
        }
    }

    handlePageContentChanged(tab, url) {
        // Handle dynamic content changes
        this.updateBadge(tab.id);
    }

    notifyPlaylistsChanged() {
        // Notify all tabs that playlists have changed
        chrome.tabs.query({}, (tabs) => {
            tabs.forEach(tab => {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'playlistsChanged'
                }).catch(() => {
                    // Ignore errors for tabs without content script
                });
            });
        });
    }

    showNotification(message, type = 'success') {
        const settings = this.getSettings();
        if (settings.showNotifications !== false) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'assets/icon48.png',
                title: 'Content Playlist Generator',
                message: message
            });
        }
    }

    showWelcomeNotification() {
        this.showNotification('Welcome! Right-click on any page to start creating playlists.');
    }

    openWelcomePage() {
        const url = chrome.runtime.getURL('welcome.html');
        chrome.tabs.create({ url });
    }

    handleUpdate(previousVersion) {
        // Handle extension updates
        console.log(`Updated from version ${previousVersion}`);
    }

    async syncPlaylists() {
        // Placeholder for future sync functionality
        const settings = await this.getSettings();
        if (settings.syncEnabled) {
            // Implement sync logic here
        }
    }
}

// Initialize background script
new PlaylistBackground();
