# Content Playlist Generator

A Chrome extension that allows users to detect, save, and organize web content into playlists. Perfect for researchers, content creators, and anyone who wants to organize their browsing discoveries.

## Features

### 🎯 Content Detection
- **Automatic Detection**: Automatically detects videos, audio, images, articles, and links on web pages
- **Manual Selection**: Right-click context menu to add specific content
- **Smart Recognition**: Recognizes content from popular platforms (YouTube, Vimeo, Spotify, etc.)
- **Multiple Content Types**: Supports videos, audio, images, articles, links, and embedded content

### 📋 Playlist Management
- **Create Multiple Playlists**: Organize content into themed collections
- **Easy Organization**: Drag-and-drop interface for reordering items
- **Rich Metadata**: Automatically captures titles, descriptions, thumbnails, and timestamps
- **Search & Filter**: Find content quickly within your playlists

### 🔄 Import/Export
- **Backup & Restore**: Export playlists as JSON files
- **Cross-Device Sync**: Import playlists on different devices
- **Data Portability**: Standard format for easy migration

### ⚙️ Customization
- **Auto-Detection Settings**: Configure automatic content detection
- **Default Playlists**: Set preferred playlist for quick additions
- **Notification Preferences**: Control extension notifications
- **Theme Support**: Light/dark mode compatibility

## Installation

### From Chrome Web Store (Coming Soon)
1. Visit the Chrome Web Store
2. Search for "Content Playlist Generator"
3. Click "Add to Chrome"

### Manual Installation (Development)
1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. Add icon files to the `assets/` folder (see assets/README.md)

## Usage

### Quick Start
1. **Install the extension** and pin it to your toolbar
2. **Visit any webpage** with content you want to save
3. **Click the extension icon** to open the popup
4. **Create your first playlist** using the "+ New" button
5. **Add content** by clicking "Add to Playlist" or "Detect Content"

### Adding Content

#### Automatic Detection
- Click "Detect Content" in the popup to scan the current page
- The extension will find videos, audio, images, articles, and links
- Click "Add" next to any detected item to save it

#### Manual Addition
- **Current Page**: Click "Add to Playlist" to save the entire page
- **Right-click Menu**: Right-click on any content and select "Add to Playlist"
- **Specific Content**: Right-click on images, videos, links, or selected text

#### Supported Content Types
- **Videos**: HTML5 videos, YouTube, Vimeo, and other embedded players
- **Audio**: HTML5 audio, Spotify embeds, podcast players
- **Images**: Photos, graphics, and visual content
- **Articles**: Blog posts, news articles, and text content
- **Links**: External links and references
- **Social Media**: Twitter posts, Instagram embeds

### Managing Playlists

#### Creating Playlists
1. Click the extension icon
2. Click "+ New" in the playlists section
3. Enter a name and optional description
4. Click "Create"

#### Organizing Content
- View playlist contents by clicking on a playlist name
- Reorder items by dragging and dropping
- Remove items with the delete button
- Edit item details by clicking the edit icon

#### Playlist Actions
- **Export**: Download playlist as JSON file
- **Import**: Upload previously exported playlists
- **Share**: Copy playlist data for sharing (future feature)
- **Sync**: Synchronize across devices (future feature)

## File Structure

```
Content Playlist Generator/
├── manifest.json              # Extension configuration
├── background.js              # Background service worker
├── content.js                 # Content detection script
├── content.css               # Content script styles
├── popup/
│   ├── popup.html            # Extension popup interface
│   ├── popup.css             # Popup styles
│   └── popup.js              # Popup functionality
├── assets/
│   ├── icon16.png            # 16x16 toolbar icon
│   ├── icon32.png            # 32x32 Windows icon
│   ├── icon48.png            # 48x48 management icon
│   ├── icon128.png           # 128x128 store icon
│   └── README.md             # Icon guidelines
└── README.md                 # This file
```

## Development

### Prerequisites
- Chrome browser (version 88+)
- Basic knowledge of HTML, CSS, and JavaScript
- Text editor or IDE

### Setup
1. Clone the repository
2. Create icon files in the `assets/` folder
3. Load the extension in Chrome developer mode
4. Make changes and reload the extension to test

### Key Components

#### Manifest (manifest.json)
- Defines extension permissions and structure
- Configures content scripts and background worker
- Sets up popup and icon references

#### Background Script (background.js)
- Handles extension lifecycle events
- Manages context menus and notifications
- Coordinates between popup and content scripts
- Handles data storage and synchronization

#### Content Script (content.js)
- Detects content on web pages
- Provides highlighting and interaction features
- Communicates with background script
- Injects UI elements for content selection

#### Popup Interface (popup/)
- Main user interface for the extension
- Displays current page information
- Shows detected content and playlists
- Provides playlist management tools

### Testing
1. Load the extension in developer mode
2. Visit various websites to test content detection
3. Try different content types (videos, images, articles)
4. Test playlist creation and management
5. Verify import/export functionality

## Permissions

The extension requires the following permissions:

- **activeTab**: Access current tab information
- **tabs**: Manage browser tabs
- **storage**: Save playlists and settings locally
- **scripting**: Inject content detection scripts
- **host_permissions**: Access all websites for content detection

## Privacy

- All data is stored locally on your device
- No data is sent to external servers
- Content URLs and metadata are saved only in your browser
- Export files contain only your playlist data

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues, feature requests, or questions:
1. Check the existing issues on GitHub
2. Create a new issue with detailed information
3. Include browser version and extension version
4. Provide steps to reproduce any bugs

## Roadmap

### Upcoming Features
- [ ] Cloud synchronization
- [ ] Playlist sharing
- [ ] Advanced search and filtering
- [ ] Content categorization
- [ ] Bulk operations
- [ ] Integration with external services
- [ ] Mobile companion app
- [ ] Collaborative playlists

### Version History
- **v1.0.0**: Initial release with core functionality
  - Content detection and playlist management
  - Import/export capabilities
  - Context menu integration
  - Basic popup interface
