// Popup script for Content Playlist Generator
class PlaylistPopup {
    constructor() {
        this.currentTab = null;
        this.playlists = [];
        this.detectedContent = [];
        this.init();
    }

    async init() {
        await this.loadCurrentTab();
        await this.loadPlaylists();
        this.setupEventListeners();
        this.updateUI();
    }

    async loadCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
        } catch (error) {
            console.error('Error loading current tab:', error);
        }
    }

    async loadPlaylists() {
        try {
            const result = await chrome.storage.local.get(['playlists']);
            this.playlists = result.playlists || [];
        } catch (error) {
            console.error('Error loading playlists:', error);
        }
    }

    async savePlaylists() {
        try {
            await chrome.storage.local.set({ playlists: this.playlists });
        } catch (error) {
            console.error('Error saving playlists:', error);
        }
    }

    setupEventListeners() {
        // Header actions
        document.getElementById('refreshBtn').addEventListener('click', () => this.refresh());
        document.getElementById('settingsBtn').addEventListener('click', () => this.openSettings());

        // Page actions
        document.getElementById('addToPlaylistBtn').addEventListener('click', () => this.addCurrentPageToPlaylist());
        document.getElementById('detectContentBtn').addEventListener('click', () => this.detectContent());

        // Playlist actions
        document.getElementById('createPlaylistBtn').addEventListener('click', () => this.showCreatePlaylistModal());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportPlaylists());
        document.getElementById('importBtn').addEventListener('click', () => this.importPlaylists());

        // Modal actions
        document.getElementById('closeModal').addEventListener('click', () => this.hideCreatePlaylistModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.hideCreatePlaylistModal());
        document.getElementById('savePlaylistBtn').addEventListener('click', () => this.createPlaylist());

        // Close modal on outside click
        document.getElementById('createPlaylistModal').addEventListener('click', (e) => {
            if (e.target.id === 'createPlaylistModal') {
                this.hideCreatePlaylistModal();
            }
        });
    }

    updateUI() {
        this.updatePageInfo();
        this.updatePlaylistList();
    }

    updatePageInfo() {
        if (this.currentTab) {
            document.getElementById('pageTitle').textContent = this.currentTab.title || 'Untitled';
            document.getElementById('pageUrl').textContent = this.currentTab.url || '';
        }
    }

    updatePlaylistList() {
        const playlistList = document.getElementById('playlistList');
        
        if (this.playlists.length === 0) {
            playlistList.innerHTML = `
                <div class="empty-state">
                    <p>No playlists yet. Create your first playlist!</p>
                </div>
            `;
            return;
        }

        playlistList.innerHTML = this.playlists.map(playlist => `
            <div class="playlist-item" data-playlist-id="${playlist.id}">
                <div class="playlist-name">${playlist.name}</div>
                <div class="playlist-count">${playlist.items.length} items</div>
            </div>
        `).join('');

        // Add click listeners to playlist items
        playlistList.querySelectorAll('.playlist-item').forEach(item => {
            item.addEventListener('click', () => {
                const playlistId = item.dataset.playlistId;
                this.openPlaylist(playlistId);
            });
        });
    }

    updateDetectedContent() {
        const detectedContentDiv = document.getElementById('detectedContent');
        const contentList = document.getElementById('contentList');

        if (this.detectedContent.length === 0) {
            detectedContentDiv.style.display = 'none';
            return;
        }

        detectedContentDiv.style.display = 'block';
        contentList.innerHTML = this.detectedContent.map((content, index) => `
            <div class="content-item">
                <div class="content-info">
                    <div class="content-title">${content.title}</div>
                    <div class="content-type">${content.type}</div>
                </div>
                <button class="btn btn-small content-add" data-content-index="${index}">Add</button>
            </div>
        `).join('');

        // Add click listeners to add buttons
        contentList.querySelectorAll('.content-add').forEach(button => {
            button.addEventListener('click', () => {
                const contentIndex = parseInt(button.dataset.contentIndex);
                this.addDetectedContentToPlaylist(contentIndex);
            });
        });
    }

    async detectContent() {
        if (!this.currentTab) return;

        try {
            // Send message to content script to detect content
            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'detectContent'
            });

            if (response && response.content) {
                this.detectedContent = response.content;
                this.updateDetectedContent();
            }
        } catch (error) {
            console.error('Error detecting content:', error);
            // Fallback: inject content script if not already injected
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: this.currentTab.id },
                    files: ['content.js']
                });
                // Try again after injection
                setTimeout(() => this.detectContent(), 500);
            } catch (injectionError) {
                console.error('Error injecting content script:', injectionError);
            }
        }
    }

    async addCurrentPageToPlaylist() {
        if (!this.currentTab) return;

        const pageItem = {
            id: Date.now().toString(),
            title: this.currentTab.title,
            url: this.currentTab.url,
            type: 'webpage',
            timestamp: new Date().toISOString(),
            favicon: this.currentTab.favIconUrl
        };

        // If only one playlist exists, add to it directly
        if (this.playlists.length === 1) {
            this.playlists[0].items.push(pageItem);
            await this.savePlaylists();
            this.updatePlaylistList();
            this.showNotification('Added to playlist!');
        } else {
            // Show playlist selection (simplified for now)
            this.showPlaylistSelection(pageItem);
        }
    }

    showPlaylistSelection(item) {
        // Simplified: add to first playlist or create new one
        if (this.playlists.length > 0) {
            this.playlists[0].items.push(item);
            this.savePlaylists();
            this.updatePlaylistList();
            this.showNotification('Added to playlist!');
        } else {
            this.showCreatePlaylistModal();
        }
    }

    addDetectedContentToPlaylist(contentIndex) {
        const content = this.detectedContent[contentIndex];
        if (!content) return;

        const contentItem = {
            id: Date.now().toString(),
            title: content.title,
            url: content.url || this.currentTab.url,
            type: content.type,
            timestamp: new Date().toISOString(),
            metadata: content.metadata || {}
        };

        this.showPlaylistSelection(contentItem);
    }

    showCreatePlaylistModal() {
        document.getElementById('createPlaylistModal').style.display = 'flex';
        document.getElementById('playlistName').focus();
    }

    hideCreatePlaylistModal() {
        document.getElementById('createPlaylistModal').style.display = 'none';
        document.getElementById('playlistName').value = '';
        document.getElementById('playlistDescription').value = '';
    }

    async createPlaylist() {
        const name = document.getElementById('playlistName').value.trim();
        const description = document.getElementById('playlistDescription').value.trim();

        if (!name) {
            alert('Please enter a playlist name');
            return;
        }

        const newPlaylist = {
            id: Date.now().toString(),
            name: name,
            description: description,
            items: [],
            created: new Date().toISOString(),
            updated: new Date().toISOString()
        };

        this.playlists.push(newPlaylist);
        await this.savePlaylists();
        this.updatePlaylistList();
        this.hideCreatePlaylistModal();
        this.showNotification('Playlist created!');
    }

    openPlaylist(playlistId) {
        // Send message to background script to open playlist view
        chrome.runtime.sendMessage({
            action: 'openPlaylist',
            playlistId: playlistId
        });
    }

    async exportPlaylists() {
        const dataStr = JSON.stringify(this.playlists, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'content-playlists.json';
        link.click();
        
        URL.revokeObjectURL(url);
        this.showNotification('Playlists exported!');
    }

    importPlaylists() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (file) {
                try {
                    const text = await file.text();
                    const importedPlaylists = JSON.parse(text);
                    
                    if (Array.isArray(importedPlaylists)) {
                        this.playlists = [...this.playlists, ...importedPlaylists];
                        await this.savePlaylists();
                        this.updatePlaylistList();
                        this.showNotification('Playlists imported!');
                    }
                } catch (error) {
                    alert('Error importing playlists: ' + error.message);
                }
            }
        };
        input.click();
    }

    async refresh() {
        await this.loadCurrentTab();
        await this.loadPlaylists();
        this.updateUI();
        this.showNotification('Refreshed!');
    }

    openSettings() {
        chrome.runtime.sendMessage({ action: 'openSettings' });
    }

    showNotification(message) {
        // Simple notification (could be enhanced with a proper notification system)
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #4caf50;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1001;
        `;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 2000);
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PlaylistPopup();
});
