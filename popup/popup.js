// Enhanced Popup script for Content Playlist Generator
class PlaylistPopup {
    constructor() {
        this.currentTab = null;
        this.playlists = [];
        this.detectedContent = [];
        this.recentItems = [];
        this.currentAudio = null;
        this.selectedPlaylistId = null;
        this.init();
    }

    async init() {
        this.showLoading('Initializing...');
        try {
            await this.loadCurrentTab();
            await this.loadPlaylists();
            await this.loadRecentItems();
            this.setupEventListeners();
            this.updateUI();
            this.detectPageContent();
        } catch (error) {
            console.error('Error initializing popup:', error);
            this.showToast('Failed to initialize', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async loadCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
        } catch (error) {
            console.error('Error loading current tab:', error);
            throw error;
        }
    }

    async loadPlaylists() {
        try {
            const result = await chrome.storage.local.get(['playlists']);
            this.playlists = result.playlists || [];
        } catch (error) {
            console.error('Error loading playlists:', error);
            throw error;
        }
    }

    async loadRecentItems() {
        try {
            const result = await chrome.storage.local.get(['recentItems']);
            this.recentItems = result.recentItems || [];
        } catch (error) {
            console.error('Error loading recent items:', error);
        }
    }

    async savePlaylists() {
        try {
            await chrome.storage.local.set({ playlists: this.playlists });
        } catch (error) {
            console.error('Error saving playlists:', error);
            throw error;
        }
    }

    async saveRecentItems() {
        try {
            // Keep only the last 10 items
            this.recentItems = this.recentItems.slice(0, 10);
            await chrome.storage.local.set({ recentItems: this.recentItems });
        } catch (error) {
            console.error('Error saving recent items:', error);
        }
    }

    setupEventListeners() {
        // Header actions
        document.getElementById('refreshBtn').addEventListener('click', () => this.refresh());
        document.getElementById('settingsBtn').addEventListener('click', () => this.openSettings());

        // Page actions
        document.getElementById('saveCurrentPageBtn').addEventListener('click', () => this.saveCurrentPage());
        document.getElementById('detectContentBtn').addEventListener('click', () => this.detectContent());

        // Playlist actions
        document.getElementById('createPlaylistBtn').addEventListener('click', () => this.showCreatePlaylistModal());
        document.getElementById('viewAllItemsBtn').addEventListener('click', () => this.viewAllItems());

        // Modal actions - Create Playlist
        document.getElementById('closeCreateModal').addEventListener('click', () => this.hideCreatePlaylistModal());
        document.getElementById('cancelCreateBtn').addEventListener('click', () => this.hideCreatePlaylistModal());
        document.getElementById('savePlaylistBtn').addEventListener('click', () => this.createPlaylist());

        // Modal actions - Playlist Selection
        document.getElementById('closeSelectionModal').addEventListener('click', () => this.hidePlaylistSelectionModal());
        document.getElementById('createNewPlaylistFromModal').addEventListener('click', () => {
            this.hidePlaylistSelectionModal();
            this.showCreatePlaylistModal();
        });

        // Audio player controls
        document.getElementById('playPauseBtn').addEventListener('click', () => this.togglePlayPause());
        document.getElementById('downloadBtn').addEventListener('click', () => this.downloadAudio());

        // Progress bar interaction
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.addEventListener('click', (e) => this.seekAudio(e));
        }

        // Close modals on outside click
        document.getElementById('createPlaylistModal').addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.hideCreatePlaylistModal();
            }
        });

        document.getElementById('playlistSelectionModal').addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.hidePlaylistSelectionModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

        // Audio element events
        const audioElement = document.getElementById('audioElement');
        if (audioElement) {
            audioElement.addEventListener('loadedmetadata', () => this.updateAudioDuration());
            audioElement.addEventListener('timeupdate', () => this.updateAudioProgress());
            audioElement.addEventListener('ended', () => this.onAudioEnded());
            audioElement.addEventListener('error', (e) => this.onAudioError(e));
        }
    }

    updateUI() {
        this.updatePageInfo();
        this.updatePlaylistGrid();
        this.updateRecentItems();
        this.updateDetectedContent();
    }

    updatePageInfo() {
        if (this.currentTab) {
            const title = this.currentTab.title || 'Untitled';
            const url = this.currentTab.url || '';

            document.getElementById('pageTitle').textContent = title;
            document.getElementById('pageUrl').textContent = this.formatUrl(url);

            // Update content type badge
            const contentType = this.detectPageType(url);
            const badge = document.getElementById('contentTypeBadge');
            badge.textContent = contentType;
            badge.className = `content-type-badge ${contentType}`;

            // Update page thumbnail
            this.updatePageThumbnail();

            // Update page meta information
            this.updatePageMeta();
        }
    }

    updatePageThumbnail() {
        const thumbnailEl = document.getElementById('pageThumbnail');
        if (this.currentTab.favIconUrl) {
            thumbnailEl.innerHTML = `<img src="${this.currentTab.favIconUrl}" alt="Page icon">`;
        } else {
            const icon = this.getPageTypeIcon(this.currentTab.url);
            thumbnailEl.innerHTML = `<div class="thumbnail-placeholder">${icon}</div>`;
        }
    }

    updatePageMeta() {
        const metaEl = document.getElementById('pageMeta');
        const meta = [];

        if (this.currentTab.url) {
            const domain = new URL(this.currentTab.url).hostname;
            meta.push(domain);
        }

        // Add more meta information as available
        metaEl.textContent = meta.join(' • ');
    }

    updatePlaylistGrid() {
        const playlistGrid = document.getElementById('playlistGrid');

        if (this.playlists.length === 0) {
            playlistGrid.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🎵</div>
                    <p>No playlists yet</p>
                    <span>Create your first playlist to get started!</span>
                </div>
            `;
            return;
        }

        playlistGrid.innerHTML = this.playlists.map(playlist => this.renderPlaylistCard(playlist)).join('');

        // Add event listeners
        this.attachPlaylistEventListeners();
    }

    renderPlaylistCard(playlist) {
        const itemCount = playlist.items.length;
        const lastUpdated = playlist.updated ? this.formatDate(playlist.updated) : 'Never';

        return `
            <div class="playlist-card" data-playlist-id="${playlist.id}">
                <div class="playlist-card-header">
                    <div class="playlist-info">
                        <h3 class="playlist-name">${this.escapeHtml(playlist.name)}</h3>
                        ${playlist.description ? `<p class="playlist-description">${this.escapeHtml(playlist.description)}</p>` : ''}
                    </div>
                </div>
                <div class="playlist-meta">
                    <span class="playlist-count">${itemCount} item${itemCount !== 1 ? 's' : ''}</span>
                    <span class="playlist-updated">Updated ${lastUpdated}</span>
                </div>
                <div class="playlist-actions">
                    <button class="generate-podcast-btn" data-playlist-id="${playlist.id}" ${itemCount === 0 ? 'disabled' : ''}>
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="5,3 19,12 5,21"></polygon>
                        </svg>
                        Generate Podcast
                    </button>
                </div>
            </div>
        `;
    }

    attachPlaylistEventListeners() {
        // Playlist card clicks
        document.querySelectorAll('.playlist-card').forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('.playlist-actions')) {
                    const playlistId = card.dataset.playlistId;
                    this.openPlaylist(playlistId);
                }
            });
        });

        // Generate podcast buttons
        document.querySelectorAll('.generate-podcast-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const playlistId = btn.dataset.playlistId;
                this.generatePodcast(playlistId);
            });
        });
    }

    updateRecentItems() {
        const itemsList = document.getElementById('recentItemsList');

        if (this.recentItems.length === 0) {
            itemsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📄</div>
                    <p>No saved items yet</p>
                    <span>Start by saving your first page!</span>
                </div>
            `;
            return;
        }

        itemsList.innerHTML = this.recentItems.slice(0, 5).map(item => this.renderSavedItem(item)).join('');

        // Add click listeners
        document.querySelectorAll('.saved-item').forEach(itemEl => {
            itemEl.addEventListener('click', () => {
                const url = itemEl.dataset.url;
                if (url) {
                    chrome.tabs.create({ url });
                }
            });
        });
    }

    renderSavedItem(item) {
        const icon = this.getContentTypeIcon(item.type);
        const timeAgo = this.formatTimeAgo(item.timestamp);

        return `
            <div class="saved-item" data-url="${item.url || ''}">
                <div class="saved-item-thumbnail">
                    ${item.thumbnail ? `<img src="${item.thumbnail}" alt="">` : icon}
                </div>
                <div class="saved-item-info">
                    <h4 class="saved-item-title">${this.escapeHtml(item.title)}</h4>
                    <div class="saved-item-meta">
                        <span>${item.type}</span>
                        <span>${timeAgo}</span>
                    </div>
                </div>
            </div>
        `;
    }

    updateDetectedContent() {
        const section = document.getElementById('detectedContentSection');
        const contentGrid = document.getElementById('contentGrid');
        const contentCount = document.getElementById('contentCount');

        if (this.detectedContent.length === 0) {
            section.style.display = 'none';
            return;
        }

        section.style.display = 'block';
        contentCount.textContent = `${this.detectedContent.length} item${this.detectedContent.length !== 1 ? 's' : ''}`;

        contentGrid.innerHTML = this.detectedContent.map((content, index) => this.renderContentItem(content, index)).join('');

        // Add click listeners
        document.querySelectorAll('.content-item').forEach(item => {
            item.addEventListener('click', () => {
                const contentIndex = parseInt(item.dataset.contentIndex);
                this.addDetectedContentToPlaylist(contentIndex);
            });
        });
    }

    renderContentItem(content, index) {
        const icon = this.getContentTypeIcon(content.type);
        const iconClass = content.type.toLowerCase();

        return `
            <div class="content-item" data-content-index="${index}">
                <div class="content-item-icon ${iconClass}">
                    ${icon}
                </div>
                <div class="content-item-info">
                    <h4 class="content-item-title">${this.escapeHtml(content.title)}</h4>
                    <p class="content-item-type">${content.type}</p>
                </div>
                <div class="content-item-actions">
                    <button class="btn btn-small btn-primary content-item-btn">Add</button>
                </div>
            </div>
        `;
    }

    async detectContent() {
        if (!this.currentTab) return;

        this.showLoading('Detecting content...');

        try {
            // Send message to content script to detect content
            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'detectContent'
            });

            if (response && response.content) {
                this.detectedContent = response.content;
                this.updateDetectedContent();
                this.showToast(`Found ${response.content.length} items`, 'success');
            } else {
                this.showToast('No content detected on this page', 'info');
            }
        } catch (error) {
            console.error('Error detecting content:', error);
            // Fallback: inject content script if not already injected
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: this.currentTab.id },
                    files: ['content.js']
                });
                // Try again after injection
                setTimeout(() => this.detectContent(), 500);
            } catch (injectionError) {
                console.error('Error injecting content script:', injectionError);
                this.showToast('Failed to detect content', 'error');
            }
        } finally {
            this.hideLoading();
        }
    }

    async detectPageContent() {
        // Automatically detect content when popup opens
        setTimeout(() => {
            this.detectContent();
        }, 1000);
    }

    async saveCurrentPage() {
        if (!this.currentTab) return;

        const pageItem = {
            id: Date.now().toString(),
            title: this.currentTab.title,
            url: this.currentTab.url,
            type: this.detectPageType(this.currentTab.url),
            timestamp: new Date().toISOString(),
            favicon: this.currentTab.favIconUrl,
            thumbnail: this.currentTab.favIconUrl
        };

        // Add to recent items
        this.recentItems.unshift(pageItem);
        await this.saveRecentItems();

        // Show playlist selection if playlists exist
        if (this.playlists.length > 0) {
            this.showPlaylistSelectionModal(pageItem);
        } else {
            // Create first playlist
            this.showCreatePlaylistModal(pageItem);
        }
    }

    async addCurrentPageToPlaylist() {
        // Legacy method - redirect to new method
        await this.saveCurrentPage();
    }

    showPlaylistSelection(item) {
        // Simplified: add to first playlist or create new one
        if (this.playlists.length > 0) {
            this.playlists[0].items.push(item);
            this.savePlaylists();
            this.updatePlaylistList();
            this.showNotification('Added to playlist!');
        } else {
            this.showCreatePlaylistModal();
        }
    }

    addDetectedContentToPlaylist(contentIndex) {
        const content = this.detectedContent[contentIndex];
        if (!content) return;

        const contentItem = {
            id: Date.now().toString(),
            title: content.title,
            url: content.url || this.currentTab.url,
            type: content.type,
            timestamp: new Date().toISOString(),
            metadata: content.metadata || {}
        };

        this.showPlaylistSelection(contentItem);
    }

    showCreatePlaylistModal(itemToAdd = null) {
        this.pendingItem = itemToAdd;
        const modal = document.getElementById('createPlaylistModal');
        modal.classList.add('show');
        modal.style.display = 'flex';

        // Focus on name input
        setTimeout(() => {
            document.getElementById('playlistName').focus();
        }, 100);
    }

    hideCreatePlaylistModal() {
        const modal = document.getElementById('createPlaylistModal');
        modal.classList.remove('show');

        setTimeout(() => {
            modal.style.display = 'none';
            this.clearCreatePlaylistForm();
        }, 300);
    }

    showPlaylistSelectionModal(item) {
        this.pendingItem = item;
        const modal = document.getElementById('playlistSelectionModal');
        const listEl = document.getElementById('playlistSelectionList');

        // Populate playlist options
        listEl.innerHTML = this.playlists.map(playlist => `
            <div class="playlist-selection-item" data-playlist-id="${playlist.id}">
                <div class="playlist-selection-icon">📋</div>
                <div class="playlist-selection-info">
                    <div class="playlist-selection-name">${this.escapeHtml(playlist.name)}</div>
                    <div class="playlist-selection-count">${playlist.items.length} items</div>
                </div>
            </div>
        `).join('');

        // Add click listeners
        listEl.querySelectorAll('.playlist-selection-item').forEach(item => {
            item.addEventListener('click', () => {
                const playlistId = item.dataset.playlistId;
                this.addItemToPlaylist(this.pendingItem, playlistId);
                this.hidePlaylistSelectionModal();
            });
        });

        modal.classList.add('show');
        modal.style.display = 'flex';
    }

    hidePlaylistSelectionModal() {
        const modal = document.getElementById('playlistSelectionModal');
        modal.classList.remove('show');

        setTimeout(() => {
            modal.style.display = 'none';
            this.pendingItem = null;
        }, 300);
    }

    clearCreatePlaylistForm() {
        document.getElementById('playlistName').value = '';
        document.getElementById('playlistDescription').value = '';
        document.getElementById('playlistCategory').value = 'general';
        this.pendingItem = null;
    }

    async createPlaylist() {
        const name = document.getElementById('playlistName').value.trim();
        const description = document.getElementById('playlistDescription').value.trim();
        const category = document.getElementById('playlistCategory').value;

        if (!name) {
            this.showToast('Please enter a playlist name', 'warning');
            return;
        }

        const newPlaylist = {
            id: Date.now().toString(),
            name: name,
            description: description,
            category: category,
            items: [],
            created: new Date().toISOString(),
            updated: new Date().toISOString()
        };

        // Add pending item if exists
        if (this.pendingItem) {
            newPlaylist.items.push(this.pendingItem);
        }

        this.playlists.push(newPlaylist);
        await this.savePlaylists();
        this.updatePlaylistGrid();
        this.hideCreatePlaylistModal();

        const message = this.pendingItem ?
            `Playlist "${name}" created and item added!` :
            `Playlist "${name}" created!`;
        this.showToast(message, 'success');
    }

    async addItemToPlaylist(item, playlistId) {
        const playlist = this.playlists.find(p => p.id === playlistId);
        if (!playlist) {
            this.showToast('Playlist not found', 'error');
            return;
        }

        // Check for duplicates
        const exists = playlist.items.some(existingItem =>
            existingItem.url === item.url && existingItem.type === item.type
        );

        if (exists) {
            this.showToast('Item already exists in playlist', 'warning');
            return;
        }

        playlist.items.push({
            ...item,
            id: Date.now().toString(),
            addedAt: new Date().toISOString()
        });

        playlist.updated = new Date().toISOString();

        await this.savePlaylists();
        this.updatePlaylistGrid();
        this.showToast(`Added to "${playlist.name}"`, 'success');
    }

    async generatePodcast(playlistId) {
        const playlist = this.playlists.find(p => p.id === playlistId);
        if (!playlist || playlist.items.length === 0) {
            this.showToast('Playlist is empty', 'warning');
            return;
        }

        this.showLoading('Generating podcast...');

        try {
            // Send request to background script to generate podcast
            const response = await chrome.runtime.sendMessage({
                action: 'generatePodcast',
                playlistId: playlistId,
                playlist: playlist
            });

            if (response.success) {
                this.showAudioPlayer(response.audioUrl, playlist.name);
                this.showToast('Podcast generated successfully!', 'success');
            } else {
                throw new Error(response.error || 'Failed to generate podcast');
            }
        } catch (error) {
            console.error('Error generating podcast:', error);
            this.showToast('Failed to generate podcast', 'error');
        } finally {
            this.hideLoading();
        }
    }

    showAudioPlayer(audioUrl, title) {
        const section = document.getElementById('audioPlayerSection');
        const audioElement = document.getElementById('audioElement');
        const titleEl = document.getElementById('playerTitle');

        titleEl.textContent = title;
        audioElement.src = audioUrl;
        section.style.display = 'block';

        this.currentAudio = {
            url: audioUrl,
            title: title,
            isPlaying: false
        };
    }

    togglePlayPause() {
        const audioElement = document.getElementById('audioElement');
        const playPauseBtn = document.getElementById('playPauseBtn');

        if (this.currentAudio.isPlaying) {
            audioElement.pause();
            this.currentAudio.isPlaying = false;
            playPauseBtn.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="5,3 19,12 5,21"></polygon>
                </svg>
            `;
        } else {
            audioElement.play();
            this.currentAudio.isPlaying = true;
            playPauseBtn.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="6" y="4" width="4" height="16"></rect>
                    <rect x="14" y="4" width="4" height="16"></rect>
                </svg>
            `;
        }
    }

    downloadAudio() {
        if (this.currentAudio && this.currentAudio.url) {
            const link = document.createElement('a');
            link.href = this.currentAudio.url;
            link.download = `${this.currentAudio.title}.mp3`;
            link.click();
        }
    }

    seekAudio(event) {
        const audioElement = document.getElementById('audioElement');
        const progressBar = event.currentTarget;
        const rect = progressBar.getBoundingClientRect();
        const percent = (event.clientX - rect.left) / rect.width;

        if (audioElement.duration) {
            audioElement.currentTime = percent * audioElement.duration;
        }
    }

    updateAudioDuration() {
        const audioElement = document.getElementById('audioElement');
        const totalTimeEl = document.getElementById('totalTime');

        if (audioElement.duration) {
            totalTimeEl.textContent = this.formatTime(audioElement.duration);
        }
    }

    updateAudioProgress() {
        const audioElement = document.getElementById('audioElement');
        const progressFill = document.getElementById('progressFill');
        const currentTimeEl = document.getElementById('currentTime');

        if (audioElement.duration) {
            const percent = (audioElement.currentTime / audioElement.duration) * 100;
            progressFill.style.width = `${percent}%`;
            currentTimeEl.textContent = this.formatTime(audioElement.currentTime);
        }
    }

    onAudioEnded() {
        this.currentAudio.isPlaying = false;
        const playPauseBtn = document.getElementById('playPauseBtn');
        playPauseBtn.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="5,3 19,12 5,21"></polygon>
            </svg>
        `;
    }

    onAudioError(error) {
        console.error('Audio error:', error);
        this.showToast('Error playing audio', 'error');
    }

    openPlaylist(playlistId) {
        // Send message to background script to open playlist view
        chrome.runtime.sendMessage({
            action: 'openPlaylist',
            playlistId: playlistId
        });
    }

    async exportPlaylists() {
        const dataStr = JSON.stringify(this.playlists, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'content-playlists.json';
        link.click();
        
        URL.revokeObjectURL(url);
        this.showNotification('Playlists exported!');
    }

    importPlaylists() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (file) {
                try {
                    const text = await file.text();
                    const importedPlaylists = JSON.parse(text);
                    
                    if (Array.isArray(importedPlaylists)) {
                        this.playlists = [...this.playlists, ...importedPlaylists];
                        await this.savePlaylists();
                        this.updatePlaylistList();
                        this.showNotification('Playlists imported!');
                    }
                } catch (error) {
                    alert('Error importing playlists: ' + error.message);
                }
            }
        };
        input.click();
    }

    async refresh() {
        await this.loadCurrentTab();
        await this.loadPlaylists();
        this.updateUI();
        this.showNotification('Refreshed!');
    }

    openSettings() {
        chrome.runtime.sendMessage({ action: 'openSettings' });
    }

    // Utility Methods
    detectPageType(url) {
        if (!url) return 'webpage';

        if (url.includes('youtube.com') || url.includes('youtu.be')) return 'video';
        if (url.includes('spotify.com')) return 'audio';
        if (url.includes('vimeo.com')) return 'video';
        if (url.includes('soundcloud.com')) return 'audio';
        if (url.includes('medium.com') || url.includes('substack.com')) return 'article';
        if (url.includes('news.') || url.includes('blog')) return 'article';
        if (url.includes('reddit.com')) return 'social';
        if (url.includes('twitter.com') || url.includes('x.com')) return 'social';

        return 'webpage';
    }

    getPageTypeIcon(url) {
        const type = this.detectPageType(url);
        const icons = {
            video: '🎥',
            audio: '🎵',
            article: '📄',
            social: '📱',
            webpage: '🌐'
        };
        return icons[type] || '🌐';
    }

    getContentTypeIcon(type) {
        const icons = {
            video: '🎥',
            audio: '🎵',
            image: '🖼️',
            article: '📄',
            link: '🔗',
            social: '📱',
            webpage: '🌐'
        };
        return icons[type] || '📄';
    }

    formatUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + urlObj.pathname;
        } catch {
            return url;
        }
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) return 'Today';
        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;

        return date.toLocaleDateString();
    }

    formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;

        return date.toLocaleDateString();
    }

    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = document.getElementById('loadingText');
        text.textContent = message;
        overlay.classList.add('show');
        overlay.style.display = 'flex';
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.remove('show');
        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);
    }

    showToast(message, type = 'info', title = null) {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        toast.innerHTML = `
            <div class="toast-icon">${icons[type] || 'ℹ'}</div>
            <div class="toast-content">
                ${title ? `<div class="toast-title">${this.escapeHtml(title)}</div>` : ''}
                <div class="toast-message">${this.escapeHtml(message)}</div>
            </div>
            <button class="toast-close">✕</button>
        `;

        // Add close functionality
        toast.querySelector('.toast-close').addEventListener('click', () => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        });

        container.appendChild(toast);

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
    }

    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + S: Save current page
        if ((event.ctrlKey || event.metaKey) && event.key === 's') {
            event.preventDefault();
            this.saveCurrentPage();
        }

        // Ctrl/Cmd + N: New playlist
        if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
            event.preventDefault();
            this.showCreatePlaylistModal();
        }

        // Escape: Close modals
        if (event.key === 'Escape') {
            this.hideCreatePlaylistModal();
            this.hidePlaylistSelectionModal();
        }

        // Space: Play/pause audio
        if (event.key === ' ' && this.currentAudio) {
            event.preventDefault();
            this.togglePlayPause();
        }
    }

    viewAllItems() {
        // Open a full view of all saved items
        chrome.runtime.sendMessage({ action: 'openItemsView' });
    }

    async refresh() {
        this.showLoading('Refreshing...');
        try {
            await this.loadCurrentTab();
            await this.loadPlaylists();
            await this.loadRecentItems();
            this.updateUI();
            this.detectPageContent();
            this.showToast('Refreshed successfully', 'success');
        } catch (error) {
            console.error('Error refreshing:', error);
            this.showToast('Failed to refresh', 'error');
        } finally {
            this.hideLoading();
        }
    }

    openSettings() {
        chrome.runtime.sendMessage({ action: 'openSettings' });
    }

    openPlaylist(playlistId) {
        chrome.runtime.sendMessage({
            action: 'openPlaylist',
            playlistId: playlistId
        });
    }
}

}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PlaylistPopup();
});

// Handle unload to cleanup
window.addEventListener('beforeunload', () => {
    const audioElement = document.getElementById('audioElement');
    if (audioElement) {
        audioElement.pause();
    }
});
