<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Playlist Generator</title>
    <link rel="stylesheet" href="popup.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">📋</div>
                    <h1>Playlist Generator</h1>
                </div>
                <div class="header-actions">
                    <button id="refreshBtn" class="icon-btn" title="Refresh">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="23 4 23 10 17 10"></polyline>
                            <polyline points="1 20 1 14 7 14"></polyline>
                            <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                        </svg>
                    </button>
                    <button id="settingsBtn" class="icon-btn" title="Settings">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="m12 1 1.68 3.36L17 6.64l-1.68 3.36L12 12l-3.32-2.28L6 7.44l1.68-3.36L12 1z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Current Page Section -->
        <section class="current-page-section">
            <div class="section-header">
                <h2>Current Page</h2>
                <div class="content-type-badge" id="contentTypeBadge">webpage</div>
            </div>
            <div class="page-card" id="pageInfo">
                <div class="page-thumbnail" id="pageThumbnail">
                    <div class="thumbnail-placeholder">🌐</div>
                </div>
                <div class="page-details">
                    <h3 class="page-title" id="pageTitle">Loading...</h3>
                    <p class="page-url" id="pageUrl"></p>
                    <div class="page-meta" id="pageMeta"></div>
                </div>
            </div>
            <div class="page-actions">
                <button id="saveCurrentPageBtn" class="btn btn-primary btn-large">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                        <polyline points="17,21 17,13 7,13 7,21"></polyline>
                        <polyline points="7,3 7,8 15,8"></polyline>
                    </svg>
                    Save Current Page
                </button>
                <button id="detectContentBtn" class="btn btn-secondary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                    Detect Content
                </button>
            </div>
        </section>

        <!-- Detected Content -->
        <section class="detected-content-section" id="detectedContentSection" style="display: none;">
            <div class="section-header">
                <h2>Detected Content</h2>
                <span class="content-count" id="contentCount">0 items</span>
            </div>
            <div class="content-grid" id="contentGrid"></div>
        </section>

        <!-- Saved Items Section -->
        <section class="saved-items-section">
            <div class="section-header">
                <h2>Recent Items</h2>
                <button id="viewAllItemsBtn" class="text-btn">View All</button>
            </div>
            <div class="items-list" id="recentItemsList">
                <div class="empty-state">
                    <div class="empty-icon">📄</div>
                    <p>No saved items yet</p>
                    <span>Start by saving your first page!</span>
                </div>
            </div>
        </section>

        <!-- Playlist Management -->
        <section class="playlist-section">
            <div class="section-header">
                <h2>My Playlists</h2>
                <button id="createPlaylistBtn" class="btn btn-small btn-primary">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    New Playlist
                </button>
            </div>
            <div class="playlist-grid" id="playlistGrid">
                <div class="empty-state">
                    <div class="empty-icon">🎵</div>
                    <p>No playlists yet</p>
                    <span>Create your first playlist to get started!</span>
                </div>
            </div>
        </section>

        <!-- Audio Player (Hidden by default) -->
        <section class="audio-player-section" id="audioPlayerSection" style="display: none;">
            <div class="audio-player">
                <div class="player-info">
                    <div class="player-thumbnail">
                        <div class="play-icon">▶️</div>
                    </div>
                    <div class="player-details">
                        <h4 id="playerTitle">Podcast Title</h4>
                        <p id="playerSubtitle">Generated from playlist</p>
                    </div>
                </div>
                <div class="player-controls">
                    <button id="playPauseBtn" class="control-btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="5,3 19,12 5,21"></polygon>
                        </svg>
                    </button>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="time-display">
                            <span id="currentTime">0:00</span>
                            <span id="totalTime">0:00</span>
                        </div>
                    </div>
                    <button id="downloadBtn" class="control-btn" title="Download">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7,10 12,15 17,10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                    </button>
                </div>
            </div>
        </section>
    </div>

    <!-- Modals -->
    <!-- Create Playlist Modal -->
    <div id="createPlaylistModal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Playlist</h3>
                <button id="closeCreateModal" class="close-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="playlistName">Playlist Name</label>
                    <input type="text" id="playlistName" placeholder="Enter playlist name" class="input-field">
                </div>
                <div class="form-group">
                    <label for="playlistDescription">Description (Optional)</label>
                    <textarea id="playlistDescription" placeholder="Describe your playlist..." class="input-field textarea"></textarea>
                </div>
                <div class="form-group">
                    <label for="playlistCategory">Category</label>
                    <select id="playlistCategory" class="input-field">
                        <option value="general">General</option>
                        <option value="education">Education</option>
                        <option value="entertainment">Entertainment</option>
                        <option value="news">News</option>
                        <option value="research">Research</option>
                        <option value="work">Work</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancelCreateBtn" class="btn btn-secondary">Cancel</button>
                <button id="savePlaylistBtn" class="btn btn-primary">Create Playlist</button>
            </div>
        </div>
    </div>

    <!-- Playlist Selection Modal -->
    <div id="playlistSelectionModal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add to Playlist</h3>
                <button id="closeSelectionModal" class="close-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="playlist-selection-list" id="playlistSelectionList">
                    <!-- Playlist options will be populated here -->
                </div>
                <div class="modal-actions">
                    <button id="createNewPlaylistFromModal" class="btn btn-outline btn-full">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Create New Playlist
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p id="loadingText">Processing...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <audio id="audioElement" style="display: none;"></audio>
    <script src="popup.js"></script>
</body>
</html>
