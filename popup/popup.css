* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 380px;
    min-height: 500px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    color: #333;
    background: #f8f9fa;
}

.container {
    padding: 16px;
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e1e5e9;
}

h1 {
    font-size: 18px;
    font-weight: 600;
    color: #1a73e8;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.icon-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
}

.icon-btn:hover {
    background-color: #f1f3f4;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    display: inline-block;
    text-decoration: none;
}

.btn-primary {
    background: #1a73e8;
    color: white;
}

.btn-primary:hover {
    background: #1557b0;
}

.btn-secondary {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.btn-secondary:hover {
    background: #f1f3f4;
}

.btn-outline {
    background: transparent;
    color: #1a73e8;
    border: 1px solid #1a73e8;
}

.btn-outline:hover {
    background: #e8f0fe;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

/* Current Page Section */
.current-page {
    margin-bottom: 20px;
}

.current-page h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #202124;
}

.page-info {
    background: white;
    border: 1px solid #dadce0;
    border-radius: 8px;
    padding: 12px;
}

.page-title {
    font-weight: 500;
    margin-bottom: 4px;
    color: #202124;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.page-url {
    font-size: 12px;
    color: #5f6368;
    margin-bottom: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.page-actions {
    display: flex;
    gap: 8px;
}

.page-actions .btn {
    flex: 1;
}

/* Detected Content */
.detected-content {
    margin-bottom: 20px;
}

.detected-content h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #202124;
}

.content-list {
    background: white;
    border: 1px solid #dadce0;
    border-radius: 8px;
    max-height: 150px;
    overflow-y: auto;
}

.content-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-item:last-child {
    border-bottom: none;
}

.content-info {
    flex: 1;
    min-width: 0;
}

.content-title {
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.content-type {
    font-size: 11px;
    color: #5f6368;
    text-transform: uppercase;
}

.content-add {
    margin-left: 8px;
}

/* Playlist Section */
.playlist-section {
    margin-bottom: 20px;
}

.playlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.playlist-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #202124;
}

.playlist-list {
    background: white;
    border: 1px solid #dadce0;
    border-radius: 8px;
    max-height: 120px;
    overflow-y: auto;
}

.playlist-item {
    padding: 10px 12px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: background-color 0.2s;
}

.playlist-item:last-child {
    border-bottom: none;
}

.playlist-item:hover {
    background-color: #f8f9fa;
}

.playlist-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.playlist-count {
    font-size: 12px;
    color: #5f6368;
}

.empty-state {
    padding: 20px;
    text-align: center;
    color: #5f6368;
    font-size: 13px;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 8px;
}

.quick-actions .btn {
    flex: 1;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 320px;
    max-width: 90%;
}

.modal-header {
    padding: 16px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 16px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #5f6368;
}

.modal-body {
    padding: 16px;
}

.input-field {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dadce0;
    border-radius: 4px;
    font-size: 14px;
    margin-bottom: 12px;
}

.input-field:focus {
    outline: none;
    border-color: #1a73e8;
}

textarea.input-field {
    resize: vertical;
    min-height: 60px;
}

.modal-footer {
    padding: 16px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f3f4;
}

::-webkit-scrollbar-thumb {
    background: #dadce0;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #bdc1c6;
}
