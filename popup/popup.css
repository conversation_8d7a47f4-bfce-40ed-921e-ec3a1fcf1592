/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #6366f1;
    --primary-hover: #5b5bd6;
    --primary-light: #e0e7ff;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;

    /* Text Colors */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;

    /* Border Colors */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e1;
    --border-dark: #94a3b8;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;

    /* Line Heights */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
}

body {
    width: 420px;
    min-height: 600px;
    font-family: var(--font-family);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    background: var(--bg-secondary);
    line-height: var(--leading-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    background: var(--bg-primary);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    padding: var(--space-4) var(--space-5);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.logo-icon {
    font-size: var(--font-size-xl);
    background: var(--primary-light);
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
}

.header h1 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.header-actions {
    display: flex;
    gap: var(--space-2);
}

.icon-btn {
    background: none;
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.icon-btn:hover {
    background-color: var(--gray-100);
    color: var(--text-primary);
}

/* Section Styles */
section {
    padding: var(--space-5);
    border-bottom: 1px solid var(--border-light);
}

section:last-child {
    border-bottom: none;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.section-header h2 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.content-type-badge {
    background: var(--primary-light);
    color: var(--primary-color);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.content-count {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.text-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: var(--font-size-xs);
    font-weight: 500;
    cursor: pointer;
    padding: var(--space-1) 0;
    transition: color 0.2s ease;
}

.text-btn:hover {
    color: var(--primary-hover);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border: none;
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-50);
    color: var(--text-primary);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-light);
}

.btn-small {
    padding: var(--space-1) var(--space-3);
    font-size: var(--font-size-xs);
}

.btn-large {
    padding: var(--space-3) var(--space-5);
    font-size: var(--font-size-base);
}

.btn-full {
    width: 100%;
}

/* Current Page Section */
.current-page-section {
    background: var(--bg-primary);
}

.page-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
    display: flex;
    gap: var(--space-3);
    transition: all 0.2s ease;
}

.page-card:hover {
    border-color: var(--border-medium);
    box-shadow: var(--shadow-sm);
}

.page-thumbnail {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    overflow: hidden;
    flex-shrink: 0;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
}

.thumbnail-placeholder {
    font-size: var(--font-size-lg);
    color: var(--text-tertiary);
}

.page-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-details {
    flex: 1;
    min-width: 0;
}

.page-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: var(--leading-tight);
}

.page-url {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin-bottom: var(--space-2);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.page-meta {
    display: flex;
    gap: var(--space-2);
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
}

.page-actions {
    display: flex;
    gap: var(--space-3);
}

.page-actions .btn {
    flex: 1;
}

/* Detected Content Section */
.detected-content-section {
    background: var(--bg-primary);
}

.content-grid {
    display: grid;
    gap: var(--space-3);
    max-height: 200px;
    overflow-y: auto;
}

.content-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    transition: all 0.2s ease;
    cursor: pointer;
}

.content-item:hover {
    border-color: var(--border-medium);
    background: var(--bg-primary);
}

.content-item-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-sm);
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.content-item-icon.video {
    background: #fee2e2;
    color: #dc2626;
}

.content-item-icon.audio {
    background: #fef3c7;
    color: #d97706;
}

.content-item-icon.image {
    background: #dcfce7;
    color: #16a34a;
}

.content-item-icon.article {
    background: #dbeafe;
    color: #2563eb;
}

.content-item-icon.link {
    background: #f3e8ff;
    color: #9333ea;
}

.content-item-info {
    flex: 1;
    min-width: 0;
}

.content-item-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: var(--font-size-sm);
}

.content-item-type {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.content-item-actions {
    display: flex;
    gap: var(--space-2);
}

.content-item-btn {
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-size-xs);
    border-radius: var(--radius-sm);
}

/* Saved Items Section */
.saved-items-section {
    background: var(--bg-primary);
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    max-height: 150px;
    overflow-y: auto;
}

.saved-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    transition: all 0.2s ease;
    cursor: pointer;
}

.saved-item:hover {
    border-color: var(--border-medium);
    background: var(--bg-primary);
}

.saved-item-thumbnail {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-sm);
    overflow: hidden;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.saved-item-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.saved-item-info {
    flex: 1;
    min-width: 0;
}

.saved-item-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: var(--font-size-sm);
}

.saved-item-meta {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    display: flex;
    gap: var(--space-2);
}

/* Playlist Section */
.playlist-section {
    background: var(--bg-primary);
}

.playlist-grid {
    display: grid;
    gap: var(--space-3);
    max-height: 200px;
    overflow-y: auto;
}

.playlist-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.playlist-card:hover {
    border-color: var(--border-medium);
    box-shadow: var(--shadow-sm);
    background: var(--bg-primary);
}

.playlist-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-3);
}

.playlist-info {
    flex: 1;
    min-width: 0;
}

.playlist-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: var(--font-size-base);
}

.playlist-description {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin-bottom: var(--space-2);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.playlist-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
}

.playlist-count {
    font-weight: 500;
}

.playlist-actions {
    display: flex;
    gap: var(--space-2);
    margin-top: var(--space-3);
}

.generate-podcast-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border: none;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.generate-podcast-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.generate-podcast-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Empty State */
.empty-state {
    padding: var(--space-8) var(--space-4);
    text-align: center;
    color: var(--text-tertiary);
}

.empty-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-3);
    opacity: 0.5;
}

.empty-state p {
    font-weight: 500;
    margin-bottom: var(--space-1);
    color: var(--text-secondary);
}

.empty-state span {
    font-size: var(--font-size-xs);
}

/* Audio Player Section */
.audio-player-section {
    background: var(--bg-primary);
    border-top: 2px solid var(--primary-color);
    position: sticky;
    bottom: 0;
    z-index: 50;
}

.audio-player {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    color: white;
    box-shadow: var(--shadow-lg);
}

.player-info {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-3);
}

.player-thumbnail {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.play-icon {
    font-size: var(--font-size-lg);
}

.player-details {
    flex: 1;
    min-width: 0;
}

.player-details h4 {
    font-weight: 600;
    margin-bottom: var(--space-1);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.player-details p {
    font-size: var(--font-size-xs);
    opacity: 0.8;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.player-controls {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-2);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.progress-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.progress-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: white;
    border-radius: 2px;
    transition: width 0.1s ease;
    width: 0%;
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    opacity: 0.8;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    width: 360px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: var(--space-5);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    cursor: pointer;
    color: var(--text-tertiary);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--gray-100);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--space-5);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--space-5);
    border-top: 1px solid var(--border-light);
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
}

/* Form Styles */
.form-group {
    margin-bottom: var(--space-4);
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    font-size: var(--font-size-sm);
}

.input-field {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    background: var(--bg-primary);
    transition: all 0.2s ease;
}

.input-field:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-field::placeholder {
    color: var(--text-tertiary);
}

.textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

select.input-field {
    cursor: pointer;
}

/* Playlist Selection List */
.playlist-selection-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
    max-height: 200px;
    overflow-y: auto;
}

.playlist-selection-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: var(--space-3);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.playlist-selection-item:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.playlist-selection-item.selected {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.playlist-selection-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-sm);
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.playlist-selection-info {
    flex: 1;
    min-width: 0;
}

.playlist-selection-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.playlist-selection-count {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
}

.modal-actions {
    margin-top: var(--space-4);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--space-3);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--space-4);
    right: var(--space-4);
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.toast {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.info {
    border-left: 4px solid var(--primary-color);
}

.toast-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    flex-shrink: 0;
    margin-top: 2px;
}

.toast.success .toast-icon {
    background: var(--success-color);
    color: white;
}

.toast.error .toast-icon {
    background: var(--error-color);
    color: white;
}

.toast.warning .toast-icon {
    background: var(--warning-color);
    color: white;
}

.toast.info .toast-icon {
    background: var(--primary-color);
    color: white;
}

.toast-content {
    flex: 1;
    min-width: 0;
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
    font-size: var(--font-size-sm);
}

.toast-message {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: var(--leading-relaxed);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.toast-close:hover {
    background: var(--gray-100);
    color: var(--text-primary);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
    transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

::-webkit-scrollbar-corner {
    background: var(--gray-100);
}

/* Responsive Design */
@media (max-width: 480px) {
    body {
        width: 100vw;
        min-height: 100vh;
    }

    .container {
        min-height: 100vh;
    }

    .header {
        padding: var(--space-3) var(--space-4);
    }

    section {
        padding: var(--space-4);
    }

    .modal-content {
        width: 95vw;
        margin: var(--space-4);
    }

    .toast {
        min-width: auto;
        max-width: calc(100vw - 2rem);
        margin: 0 var(--space-4);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1e293b;
        --bg-secondary: #334155;
        --bg-tertiary: #475569;

        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
        --text-tertiary: #94a3b8;

        --border-light: #334155;
        --border-medium: #475569;
        --border-dark: #64748b;

        --gray-50: #0f172a;
        --gray-100: #1e293b;
        --gray-200: #334155;
        --gray-300: #475569;
    }

    .page-card,
    .content-item,
    .saved-item,
    .playlist-card {
        background: var(--bg-secondary);
    }

    .page-card:hover,
    .content-item:hover,
    .saved-item:hover,
    .playlist-card:hover {
        background: var(--bg-tertiary);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease;
}

.slide-up {
    animation: slideUp 0.3s ease;
}

.scale-in {
    animation: scaleIn 0.2s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-center {
    text-align: center;
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-2 {
    gap: var(--space-2);
}

.gap-3 {
    gap: var(--space-3);
}

.w-full {
    width: 100%;
}
