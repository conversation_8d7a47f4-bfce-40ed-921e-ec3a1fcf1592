<!DOCTYPE html>
<html>
<head>
    <title>Fix Chrome Extension Icons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f2f5;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .error-box {
            background: #fee;
            border: 1px solid #fcc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            color: #c33;
        }
        .solution-box {
            background: #efe;
            border: 1px solid #cfc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            color: #363;
        }
        .big-button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            transition: background 0.3s;
        }
        .big-button:hover {
            background: #5b5bd6;
        }
        .icon-preview {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #6366f1;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Fix Chrome Extension Icons</h1>
            <p>Solve the "Could not load icon" error instantly</p>
        </div>

        <div class="error-box">
            <strong>❌ Current Error:</strong><br>
            Could not load icon 'assets/icon16.png' specified in 'icons'.<br>
            Impossibile caricare il file manifest.
        </div>

        <div class="solution-box">
            <strong>✅ Solution:</strong><br>
            Click the button below to automatically download all required icon files.
        </div>

        <button class="big-button" onclick="createAndDownloadIcons()">
            🚀 Create & Download All Icons Now
        </button>

        <div id="iconPreview" class="icon-preview"></div>

        <div id="successMessage" class="success">
            <strong>✅ Success!</strong> All icons have been downloaded to your Downloads folder.
        </div>

        <div class="instructions">
            <h3>📋 Next Steps:</h3>
            <div class="step">
                <strong>Step 1:</strong> Check your Downloads folder for 4 icon files:
                <ul>
                    <li>icon16.png</li>
                    <li>icon32.png</li>
                    <li>icon48.png</li>
                    <li>icon128.png</li>
                </ul>
            </div>
            <div class="step">
                <strong>Step 2:</strong> Move these files to your extension's <code>assets/</code> folder
            </div>
            <div class="step">
                <strong>Step 3:</strong> Go to <code>chrome://extensions/</code> and click the refresh button on your extension
            </div>
            <div class="step">
                <strong>Step 4:</strong> The error should be gone and your extension will work!
            </div>
        </div>
    </div>

    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // Create purple gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#6366f1');
            gradient.addColorStop(1, '#8b5cf6');
            
            // Fill background
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // Add white border for better visibility
            ctx.strokeStyle = 'rgba(255,255,255,0.2)';
            ctx.lineWidth = 1;
            ctx.strokeRect(0, 0, size, size);

            // Add simple "P" for Playlist
            ctx.fillStyle = 'white';
            ctx.font = `bold ${Math.max(8, size * 0.6)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('P', size / 2, size / 2);

            return canvas;
        }

        function downloadIcon(canvas, filename) {
            return new Promise((resolve) => {
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                    resolve();
                }, 'image/png');
            });
        }

        async function createAndDownloadIcons() {
            const sizes = [16, 32, 48, 128];
            const preview = document.getElementById('iconPreview');
            const successMessage = document.getElementById('successMessage');
            
            preview.innerHTML = '<p>Creating icons...</p>';

            for (let i = 0; i < sizes.length; i++) {
                const size = sizes[i];
                const canvas = createIcon(size);
                const filename = `icon${size}.png`;
                
                // Add to preview
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                iconItem.innerHTML = `
                    <div style="margin-bottom: 8px;">${canvas.outerHTML}</div>
                    <div><strong>${filename}</strong></div>
                    <div>${size}x${size}px</div>
                    <div style="color: green; margin-top: 5px;">✓ Downloaded</div>
                `;
                
                if (i === 0) {
                    preview.innerHTML = '';
                }
                preview.appendChild(iconItem);
                
                // Download with delay
                await new Promise(resolve => setTimeout(resolve, 500));
                await downloadIcon(canvas, filename);
            }

            // Show success message
            successMessage.style.display = 'block';
            
            // Scroll to success message
            successMessage.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
