#!/usr/bin/env python3
"""
Simple script to generate placeholder icons for Chrome extension
Requires: pip install Pillow
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
except ImportError:
    print("Please install Pillow: pip install Pillow")
    exit(1)

def create_icon(size, filename):
    """Create a simple icon with the specified size"""
    
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Create gradient-like background
    primary_color = (99, 102, 241)  # #6366f1
    secondary_color = (139, 92, 246)  # #8b5cf6
    
    # Draw rounded rectangle background
    margin = size // 8
    draw.rounded_rectangle(
        [margin, margin, size - margin, size - margin],
        radius=size // 6,
        fill=primary_color
    )
    
    # Add playlist icon (simplified)
    icon_size = size // 2
    icon_x = (size - icon_size) // 2
    icon_y = (size - icon_size) // 2
    
    # Draw list lines
    line_width = max(1, size // 16)
    line_spacing = icon_size // 4
    
    for i in range(3):
        y_pos = icon_y + (i * line_spacing)
        draw.rectangle(
            [icon_x, y_pos, icon_x + icon_size - size//8, y_pos + line_width],
            fill='white'
        )
    
    # Draw play button overlay
    play_size = size // 4
    play_x = icon_x + icon_size - play_size
    play_y = icon_y + icon_size - play_size
    
    # Triangle for play button
    triangle_points = [
        (play_x, play_y),
        (play_x, play_y + play_size),
        (play_x + play_size, play_y + play_size // 2)
    ]
    draw.polygon(triangle_points, fill='white')
    
    return img

def main():
    """Generate all required icon sizes"""
    
    # Create assets directory if it doesn't exist
    assets_dir = 'assets'
    if not os.path.exists(assets_dir):
        os.makedirs(assets_dir)
        print(f"Created {assets_dir} directory")
    
    # Icon sizes required by Chrome extensions
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        filename = f"{assets_dir}/icon{size}.png"
        icon = create_icon(size, filename)
        icon.save(filename, 'PNG')
        print(f"Generated {filename} ({size}x{size})")
    
    print("\n✅ All icons generated successfully!")
    print("\nNext steps:")
    print("1. Check the assets/ folder for the generated icons")
    print("2. Reload your Chrome extension")
    print("3. The extension should now load without errors")

if __name__ == "__main__":
    main()
